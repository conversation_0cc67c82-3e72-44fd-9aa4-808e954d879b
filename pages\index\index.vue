<template>
  <view class="container">
    <!-- 顶部标题 -->
    <view class="header">
      <text class="title">家庭记账本</text>
      <text class="subtitle">简单记录，轻松管理</text>
    </view>

    <!-- 余额显示 -->
    <view class="balance-card">
      <view class="balance-label">当前余额</view>
      <view class="balance-amount">¥{{ totalBalance.toFixed(0) }}</view>
      <view class="balance-detail">
        <view class="balance-item">
          <view class="item-label">本月收入</view>
          <view class="item-value income">¥{{ monthIncome.toFixed(0) }}</view>
        </view>
        <view class="balance-item">
          <view class="item-label">本月支出</view>
          <view class="item-value expense">¥{{ monthExpense.toFixed(0) }}</view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="actions">
      <view class="action-btn income-btn" @click="quickAdd('income')">
        <text class="btn-text">记录收入</text>
      </view>
      <view class="action-btn expense-btn" @click="quickAdd('expense')">
        <text class="btn-text">记录支出</text>
      </view>
    </view>

    <!-- 最近记录 -->
    <view class="recent-section">
      <view class="section-header">
        <text class="section-title">最近记录</text>
      </view>

      <view v-if="recentRecords.length === 0" class="empty-state">
        <text class="empty-text">还没有记录</text>
        <text class="empty-hint">点击上方按钮开始记账</text>
      </view>

      <view v-else class="record-list">
        <view
          v-for="(record, index) in recentRecords"
          :key="index"
          class="record-item"
          @click="viewRecord(record)"
        >
          <view class="record-info">
            <view class="record-category">{{ record.category }}</view>
            <view class="record-note">{{ record.note || '无备注' }}</view>
            <view class="record-date">{{ formatDate(record.date) }}</view>
          </view>
          <view class="record-amount" :class="record.type">
            {{ record.type === 'income' ? '+' : '-' }}¥{{ record.amount.toFixed(0) }}
          </view>
        </view>

        <!-- 查看更多按钮 -->
        <view v-if="records.length > 3" class="view-more-btn" @click="viewMoreRecords">
          <text class="view-more-text">查看全部记录</text>
          <text class="view-more-arrow">→</text>
        </view>
      </view>
    </view>

    <!-- 自定义TabBar -->
    <custom-tab-bar></custom-tab-bar>
  </view>
</template>

<script>
import CustomTabBar from '../../custom-tab-bar/index.vue'

export default {
  components: {
    CustomTabBar
  },
  data() {
    return {
      records: [],
      totalBalance: 0,
      monthIncome: 0,
      monthExpense: 0,
      recentRecords: []
    }
  },

  onShow() {
    this.loadData()
    // 通知TabBar更新选中状态
    this.$nextTick(() => {
      uni.$emit('tabBarUpdate')
    })
  },
  methods: {
    loadData() {
      // 从本地存储加载数据
      this.records = uni.getStorageSync('accountRecords') || []
      this.calculateBalance()
      this.getRecentRecords()
    },

    calculateBalance() {
      const now = new Date()
      const currentMonth = now.getMonth()
      const currentYear = now.getFullYear()

      let totalIncomeAmount = 0
      let totalExpenseAmount = 0
      let monthIncomeAmount = 0
      let monthExpenseAmount = 0

      this.records.forEach(record => {
        const recordDate = new Date(record.date)
        const amount = parseFloat(record.amount)

        if (record.type === 'income') {
          totalIncomeAmount += amount
          if (recordDate.getMonth() === currentMonth && recordDate.getFullYear() === currentYear) {
            monthIncomeAmount += amount
          }
        } else {
          totalExpenseAmount += amount
          if (recordDate.getMonth() === currentMonth && recordDate.getFullYear() === currentYear) {
            monthExpenseAmount += amount
          }
        }
      })

      this.totalBalance = totalIncomeAmount - totalExpenseAmount
      this.monthIncome = monthIncomeAmount
      this.monthExpense = monthExpenseAmount
    },

    getRecentRecords() {
      // 获取最近3条记录
      this.recentRecords = this.records
        .sort((a, b) => new Date(b.createTime || b.date) - new Date(a.createTime || a.date))
        .slice(0, 3)
    },

    viewMoreRecords() {
      uni.switchTab({
        url: '/pages/history/history'
      })
    },

    quickAdd(type) {
      console.log('quickAdd clicked:', type)

      // 直接跳转到记账页面
      uni.switchTab({
        url: '/pages/add/add',
        success: () => {
          console.log('跳转成功')
          // 可以通过全局变量或事件传递类型
          getApp().globalData = getApp().globalData || {}
          getApp().globalData.addType = type
        },
        fail: (err) => {
          console.error('switchTab失败:', err)
          // 如果switchTab失败，说明记账页面不在tabBar中，使用navigateTo
          uni.navigateTo({
            url: `/pages/add/add?type=${type}`,
            success: () => {
              console.log('navigateTo成功')
            },
            fail: (navErr) => {
              console.error('navigateTo也失败:', navErr)
              uni.showToast({
                title: '页面跳转失败',
                icon: 'none'
              })
            }
          })
        }
      })
    },

    viewRecord(record) {
      const typeText = record.type === 'income' ? '收入' : '支出'
      const amountText = `${record.type === 'income' ? '+' : '-'}¥${record.amount.toFixed(0)}`

      uni.showModal({
        title: `${typeText}记录`,
        content: `分类：${record.category}\n金额：${amountText}\n备注：${record.note || '无'}\n日期：${this.formatDate(record.date)}`,
        showCancel: false,
        confirmText: '知道了'
      })
    },

    formatDate(dateStr) {
      const date = new Date(dateStr)
      const today = new Date()
      const yesterday = new Date(today)
      yesterday.setDate(yesterday.getDate() - 1)

      if (date.toDateString() === today.toDateString()) {
        return '今天'
      } else if (date.toDateString() === yesterday.toDateString()) {
        return '昨天'
      } else {
        return `${date.getMonth() + 1}月${date.getDate()}日`
      }
    }
  }
}
</script>

<style scoped>
.container {
  background: linear-gradient(135deg, #fefefe 0%, #f8f8f8 25%, #f5f5f5 50%, #f2f2f2 75%, #efefef 100%);
  background-size: 400% 400%;
  animation: gradientShift 20s ease-in-out infinite;
  min-height: 100vh;
  padding-bottom: 140rpx; /* 为TabBar留出空间 */
  position: relative;
  overflow: hidden;
}

.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.9) 0%, transparent 35%),
    radial-gradient(circle at 80% 30%, rgba(248, 250, 252, 0.8) 0%, transparent 40%),
    radial-gradient(circle at 40% 80%, rgba(241, 245, 249, 0.7) 0%, transparent 45%),
    radial-gradient(circle at 70% 70%, rgba(255, 255, 255, 0.6) 0%, transparent 30%);
  pointer-events: none;
  animation: backgroundFloat 25s ease-in-out infinite;
}

.container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(8px 8px at 30px 40px, rgba(255, 255, 255, 0.8), transparent),
    radial-gradient(6px 6px at 60px 80px, rgba(255, 255, 255, 0.6), transparent),
    radial-gradient(4px 4px at 100px 50px, rgba(255, 255, 255, 0.9), transparent),
    radial-gradient(7px 7px at 140px 90px, rgba(255, 255, 255, 0.7), transparent),
    radial-gradient(5px 5px at 180px 40px, rgba(255, 255, 255, 0.8), transparent),
    radial-gradient(6px 6px at 200px 70px, rgba(255, 255, 255, 0.5), transparent),
    radial-gradient(3px 3px at 250px 30px, rgba(255, 255, 255, 0.9), transparent),
    radial-gradient(9px 9px at 280px 100px, rgba(255, 255, 255, 0.6), transparent);
  background-repeat: repeat;
  background-size: 320px 160px;
  animation: snowfall 25s linear infinite;
  pointer-events: none;
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  25% { background-position: 100% 25%; }
  50% { background-position: 50% 100%; }
  75% { background-position: 25% 0%; }
}

@keyframes backgroundFloat {
  0%, 100% {
    transform: translateX(0) translateY(0) scale(1) rotate(0deg);
    opacity: 0.9;
  }
  25% {
    transform: translateX(15px) translateY(-8px) scale(1.03) rotate(1deg);
    opacity: 0.95;
  }
  50% {
    transform: translateX(-8px) translateY(12px) scale(0.97) rotate(-1deg);
    opacity: 0.85;
  }
  75% {
    transform: translateX(12px) translateY(-5px) scale(1.01) rotate(0.5deg);
    opacity: 0.92;
  }
}

@keyframes snowfall {
  0% {
    transform: translateY(-120vh) translateX(-40px) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(120vh) translateX(160px) rotate(180deg);
    opacity: 0;
  }
}

/* 顶部标题 */
.header {
  position: relative;
  height: 280rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.title {
  font-size: 72rpx;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 24rpx;
  letter-spacing: 6rpx;
  text-shadow: 0 3rpx 12rpx rgba(0, 0, 0, 0.15);
  z-index: 2;
}

.subtitle {
  font-size: 40rpx;
  color: #2d3748;
  font-weight: 400;
  letter-spacing: 3rpx;
  text-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
  z-index: 2;
}

/* 余额卡片 */
.balance-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(40rpx);
  -webkit-backdrop-filter: blur(40rpx);
  margin: 32rpx;
  padding: 40rpx;
  border-radius: 32rpx;
  text-align: center;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 20rpx 60rpx rgba(0, 0, 0, 0.1),
    0 8rpx 24rpx rgba(0, 0, 0, 0.08),
    inset 0 2rpx 0 rgba(255, 255, 255, 0.3);
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

.balance-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.4) 0%,
    rgba(255, 255, 255, 0.05) 30%,
    rgba(255, 255, 255, 0.2) 70%,
    rgba(255, 255, 255, 0.3) 100%);
  border-radius: 40rpx;
  pointer-events: none;
}

.balance-card::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(
    from 0deg,
    transparent 0deg,
    rgba(255, 255, 255, 0.1) 60deg,
    rgba(255, 255, 255, 0.2) 120deg,
    transparent 180deg,
    rgba(255, 255, 255, 0.05) 240deg,
    transparent 360deg
  );
  animation: shimmer 8s linear infinite;
  pointer-events: none;
}

@keyframes shimmer {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.balance-card:hover {
  transform: translateY(-6rpx) scale(1.02);
  box-shadow:
    0 32rpx 80rpx rgba(0, 0, 0, 0.06),
    0 12rpx 32rpx rgba(0, 0, 0, 0.04),
    0 6rpx 16rpx rgba(0, 0, 0, 0.03),
    inset 0 2rpx 0 rgba(255, 255, 255, 0.95),
    inset 0 -1rpx 0 rgba(255, 255, 255, 0.6);
}

.balance-label {
  font-size: 44rpx;
  margin-bottom: 28rpx;
  color: #2d3748;
  font-weight: 500;
  letter-spacing: 2rpx;
  position: relative;
  z-index: 2;
}

.balance-amount {
  font-size: 96rpx;
  font-weight: 700;
  margin: 40rpx 0;
  color: #1a202c;
  letter-spacing: 3rpx;
  text-shadow: 0 3rpx 12rpx rgba(0, 0, 0, 0.15);
  position: relative;
  z-index: 2;
}

.balance-detail {
  display: flex;
  justify-content: space-around;
  margin-top: 40rpx;
  padding-top: 32rpx;
  border-top: 1rpx solid rgba(255, 255, 255, 0.4);
  position: relative;
  z-index: 2;
}

.balance-item {
  text-align: center;
}

.item-label {
  font-size: 36rpx;
  color: #2d3748;
  margin-bottom: 20rpx;
  font-weight: 500;
  letter-spacing: 1rpx;
}

.item-value {
  font-size: 48rpx;
  font-weight: 600;
  color: #1a202c;
  letter-spacing: 2rpx;
}

/* 操作按钮 */
.actions {
  padding: 0 24rpx;
  display: flex;
  flex-direction: row;
  gap: 16rpx;
}

.action-btn {
  flex: 1;
  height: 140rpx;
  border: none;
  border-radius: 32rpx;
  font-size: 42rpx;
  font-weight: 600;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
  box-shadow:
    0 12rpx 40rpx rgba(0, 0, 0, 0.15),
    0 4rpx 12rpx rgba(0, 0, 0, 0.1),
    inset 0 2rpx 0 rgba(255, 255, 255, 0.4);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  transform: translateY(0);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.1) 100%);
  border-radius: 24rpx;
  pointer-events: none;
}

.action-btn:active {
  transform: translateY(2rpx);
  box-shadow:
    0 4rpx 16rpx rgba(0, 0, 0, 0.15),
    0 1rpx 4rpx rgba(0, 0, 0, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.2);
}

.income-btn {
  background: linear-gradient(135deg,
    rgba(52, 211, 153, 0.9) 0%,
    rgba(16, 185, 129, 0.9) 100%);
}

.expense-btn {
  background: linear-gradient(135deg,
    rgba(251, 146, 60, 0.9) 0%,
    rgba(245, 101, 101, 0.9) 100%);
}

.btn-text {
  font-size: 42rpx;
  font-weight: 600;
  letter-spacing: 2rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
  position: relative;
  z-index: 2;
}

/* 最近记录 */
.recent-section {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(40rpx);
  -webkit-backdrop-filter: blur(40rpx);
  border-radius: 32rpx;
  margin: 32rpx;
  padding: 40rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 20rpx 60rpx rgba(0, 0, 0, 0.1),
    0 8rpx 24rpx rgba(0, 0, 0, 0.08),
    inset 0 2rpx 0 rgba(255, 255, 255, 0.3);
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

.recent-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0.2) 100%);
  pointer-events: none;
}

.section-header {
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  padding: 32rpx 40rpx;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.4);
  position: relative;
  z-index: 2;
}

.section-title {
  font-size: 48rpx;
  font-weight: 600;
  color: #1a202c;
  letter-spacing: 3rpx;
}

.record-list {
  padding: 24rpx 0;
  position: relative;
  z-index: 2;
}

.record-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 36rpx 40rpx;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.record-item:last-child {
  border-bottom: none;
}

.record-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.record-info {
  flex: 1;
}

.record-category {
  font-size: 44rpx;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 16rpx;
  letter-spacing: 1rpx;
}

.record-note {
  font-size: 36rpx;
  color: #2d3748;
  margin-bottom: 12rpx;
  font-weight: 400;
}

.record-date {
  font-size: 32rpx;
  color: #4a5568;
  font-weight: 500;
}

.record-amount {
  font-size: 48rpx;
  font-weight: 700;
  letter-spacing: 2rpx;
}

.record-amount.income {
  color: #38a169;
}

.record-amount.expense {
  color: #e53e3e;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 96rpx 48rpx;
  color: #4a5568;
  position: relative;
  z-index: 2;
}

.empty-text {
  font-size: 36rpx;
  margin-bottom: 24rpx;
  font-weight: 300;
  letter-spacing: 1rpx;
}

.empty-hint {
  font-size: 30rpx;
  opacity: 0.8;
  font-weight: 300;
}

/* 查看更多按钮 */
.view-more-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 32rpx;
  margin-top: 16rpx;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  z-index: 2;
}

.view-more-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
}

.view-more-btn:active {
  transform: translateY(0);
  background: rgba(255, 255, 255, 0.2);
}

.view-more-text {
  font-size: 28rpx;
  color: #4a5568;
  font-weight: 300;
  letter-spacing: 1rpx;
}

.view-more-arrow {
  font-size: 24rpx;
  color: #4a5568;
  transition: transform 0.3s ease;
}

.view-more-btn:hover .view-more-arrow {
  transform: translateX(4rpx);
}
</style>
