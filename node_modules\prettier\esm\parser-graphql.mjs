var X=Object.getOwnPropertyNames,re=(l,T)=>function(){return l&&(T=(0,l[X(l)[0]])(l=0)),T},K=(l,T)=>function(){return T||(0,l[X(l)[0]])((T={exports:{}}).exports,T),T.exports},L=re({"<define:process>"(){}}),ie=K({"src/common/parser-create-error.js"(l,T){"use strict";L();function a(p,r){let _=new SyntaxError(p+" ("+r.start.line+":"+r.start.column+")");return _.loc=r,_}T.exports=a}}),ae=K({"src/utils/try-combinations.js"(l,T){"use strict";L();function a(){let p;for(var r=arguments.length,_=new Array(r),E=0;E<r;E++)_[E]=arguments[E];for(let[k,g]of _.entries())try{return{result:g()}}catch(D){k===0&&(p=D)}return{error:p}}T.exports=a}}),oe=K({"src/language-graphql/pragma.js"(l,T){"use strict";L();function a(r){return/^\s*#[^\S\n]*@(?:format|prettier)\s*(?:\n|$)/.test(r)}function p(r){return`# @format

`+r}T.exports={hasPragma:a,insertPragma:p}}}),se=K({"src/language-graphql/loc.js"(l,T){"use strict";L();function a(r){return typeof r.start=="number"?r.start:r.loc&&r.loc.start}function p(r){return typeof r.end=="number"?r.end:r.loc&&r.loc.end}T.exports={locStart:a,locEnd:p}}}),ce=K({"node_modules/graphql/jsutils/isObjectLike.js"(l){"use strict";L(),Object.defineProperty(l,"__esModule",{value:!0}),l.default=a;function T(p){return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?T=function(_){return typeof _}:T=function(_){return _&&typeof Symbol=="function"&&_.constructor===Symbol&&_!==Symbol.prototype?"symbol":typeof _},T(p)}function a(p){return T(p)=="object"&&p!==null}}}),H=K({"node_modules/graphql/polyfills/symbols.js"(l){"use strict";L(),Object.defineProperty(l,"__esModule",{value:!0}),l.SYMBOL_TO_STRING_TAG=l.SYMBOL_ASYNC_ITERATOR=l.SYMBOL_ITERATOR=void 0;var T=typeof Symbol=="function"&&Symbol.iterator!=null?Symbol.iterator:"@@iterator";l.SYMBOL_ITERATOR=T;var a=typeof Symbol=="function"&&Symbol.asyncIterator!=null?Symbol.asyncIterator:"@@asyncIterator";l.SYMBOL_ASYNC_ITERATOR=a;var p=typeof Symbol=="function"&&Symbol.toStringTag!=null?Symbol.toStringTag:"@@toStringTag";l.SYMBOL_TO_STRING_TAG=p}}),z=K({"node_modules/graphql/language/location.js"(l){"use strict";L(),Object.defineProperty(l,"__esModule",{value:!0}),l.getLocation=T;function T(a,p){for(var r=/\r\n|[\n\r]/g,_=1,E=p+1,k;(k=r.exec(a.body))&&k.index<p;)_+=1,E=p+1-(k.index+k[0].length);return{line:_,column:E}}}}),ue=K({"node_modules/graphql/language/printLocation.js"(l){"use strict";L(),Object.defineProperty(l,"__esModule",{value:!0}),l.printLocation=a,l.printSourceLocation=p;var T=z();function a(k){return p(k.source,(0,T.getLocation)(k.source,k.start))}function p(k,g){var D=k.locationOffset.column-1,O=_(D)+k.body,S=g.line-1,I=k.locationOffset.line-1,h=g.line+I,N=g.line===1?D:0,i=g.column+N,u="".concat(k.name,":").concat(h,":").concat(i,`
`),e=O.split(/\r\n|[\n\r]/g),n=e[S];if(n.length>120){for(var t=Math.floor(i/80),s=i%80,y=[],f=0;f<n.length;f+=80)y.push(n.slice(f,f+80));return u+r([["".concat(h),y[0]]].concat(y.slice(1,t+1).map(function(m){return["",m]}),[[" ",_(s-1)+"^"],["",y[t+1]]]))}return u+r([["".concat(h-1),e[S-1]],["".concat(h),n],["",_(i-1)+"^"],["".concat(h+1),e[S+1]]])}function r(k){var g=k.filter(function(O){var S=O[0],I=O[1];return I!==void 0}),D=Math.max.apply(Math,g.map(function(O){var S=O[0];return S.length}));return g.map(function(O){var S=O[0],I=O[1];return E(D,S)+(I?" | "+I:" |")}).join(`
`)}function _(k){return Array(k+1).join(" ")}function E(k,g){return _(k-g.length)+g}}}),$=K({"node_modules/graphql/error/GraphQLError.js"(l){"use strict";L();function T(f){return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?T=function(o){return typeof o}:T=function(o){return o&&typeof Symbol=="function"&&o.constructor===Symbol&&o!==Symbol.prototype?"symbol":typeof o},T(f)}Object.defineProperty(l,"__esModule",{value:!0}),l.printError=y,l.GraphQLError=void 0;var a=E(ce()),p=H(),r=z(),_=ue();function E(f){return f&&f.__esModule?f:{default:f}}function k(f,m){if(!(f instanceof m))throw new TypeError("Cannot call a class as a function")}function g(f,m){for(var o=0;o<m.length;o++){var d=m[o];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(f,d.key,d)}}function D(f,m,o){return m&&g(f.prototype,m),o&&g(f,o),f}function O(f,m){if(typeof m!="function"&&m!==null)throw new TypeError("Super expression must either be null or a function");f.prototype=Object.create(m&&m.prototype,{constructor:{value:f,writable:!0,configurable:!0}}),m&&n(f,m)}function S(f){var m=u();return function(){var d=t(f),c;if(m){var v=t(this).constructor;c=Reflect.construct(d,arguments,v)}else c=d.apply(this,arguments);return I(this,c)}}function I(f,m){return m&&(T(m)==="object"||typeof m=="function")?m:h(f)}function h(f){if(f===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return f}function N(f){var m=typeof Map=="function"?new Map:void 0;return N=function(d){if(d===null||!e(d))return d;if(typeof d!="function")throw new TypeError("Super expression must either be null or a function");if(typeof m<"u"){if(m.has(d))return m.get(d);m.set(d,c)}function c(){return i(d,arguments,t(this).constructor)}return c.prototype=Object.create(d.prototype,{constructor:{value:c,enumerable:!1,writable:!0,configurable:!0}}),n(c,d)},N(f)}function i(f,m,o){return u()?i=Reflect.construct:i=function(c,v,A){var x=[null];x.push.apply(x,v);var b=Function.bind.apply(c,x),P=new b;return A&&n(P,A.prototype),P},i.apply(null,arguments)}function u(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch{return!1}}function e(f){return Function.toString.call(f).indexOf("[native code]")!==-1}function n(f,m){return n=Object.setPrototypeOf||function(d,c){return d.__proto__=c,d},n(f,m)}function t(f){return t=Object.setPrototypeOf?Object.getPrototypeOf:function(o){return o.__proto__||Object.getPrototypeOf(o)},t(f)}var s=function(f){O(o,f);var m=S(o);function o(d,c,v,A,x,b,P){var U,q,V,G,C;k(this,o),C=m.call(this,d);var R=Array.isArray(c)?c.length!==0?c:void 0:c?[c]:void 0,Y=v;if(!Y&&R){var J;Y=(J=R[0].loc)===null||J===void 0?void 0:J.source}var F=A;!F&&R&&(F=R.reduce(function(w,M){return M.loc&&w.push(M.loc.start),w},[])),F&&F.length===0&&(F=void 0);var B;A&&v?B=A.map(function(w){return(0,r.getLocation)(v,w)}):R&&(B=R.reduce(function(w,M){return M.loc&&w.push((0,r.getLocation)(M.loc.source,M.loc.start)),w},[]));var j=P;if(j==null&&b!=null){var Q=b.extensions;(0,a.default)(Q)&&(j=Q)}return Object.defineProperties(h(C),{name:{value:"GraphQLError"},message:{value:d,enumerable:!0,writable:!0},locations:{value:(U=B)!==null&&U!==void 0?U:void 0,enumerable:B!=null},path:{value:x!=null?x:void 0,enumerable:x!=null},nodes:{value:R!=null?R:void 0},source:{value:(q=Y)!==null&&q!==void 0?q:void 0},positions:{value:(V=F)!==null&&V!==void 0?V:void 0},originalError:{value:b},extensions:{value:(G=j)!==null&&G!==void 0?G:void 0,enumerable:j!=null}}),b!=null&&b.stack?(Object.defineProperty(h(C),"stack",{value:b.stack,writable:!0,configurable:!0}),I(C)):(Error.captureStackTrace?Error.captureStackTrace(h(C),o):Object.defineProperty(h(C),"stack",{value:Error().stack,writable:!0,configurable:!0}),C)}return D(o,[{key:"toString",value:function(){return y(this)}},{key:p.SYMBOL_TO_STRING_TAG,get:function(){return"Object"}}]),o}(N(Error));l.GraphQLError=s;function y(f){var m=f.message;if(f.nodes)for(var o=0,d=f.nodes;o<d.length;o++){var c=d[o];c.loc&&(m+=`

`+(0,_.printLocation)(c.loc))}else if(f.source&&f.locations)for(var v=0,A=f.locations;v<A.length;v++){var x=A[v];m+=`

`+(0,_.printSourceLocation)(f.source,x)}return m}}}),W=K({"node_modules/graphql/error/syntaxError.js"(l){"use strict";L(),Object.defineProperty(l,"__esModule",{value:!0}),l.syntaxError=a;var T=$();function a(p,r,_){return new T.GraphQLError("Syntax Error: ".concat(_),void 0,p,[r])}}}),le=K({"node_modules/graphql/language/kinds.js"(l){"use strict";L(),Object.defineProperty(l,"__esModule",{value:!0}),l.Kind=void 0;var T=Object.freeze({NAME:"Name",DOCUMENT:"Document",OPERATION_DEFINITION:"OperationDefinition",VARIABLE_DEFINITION:"VariableDefinition",SELECTION_SET:"SelectionSet",FIELD:"Field",ARGUMENT:"Argument",FRAGMENT_SPREAD:"FragmentSpread",INLINE_FRAGMENT:"InlineFragment",FRAGMENT_DEFINITION:"FragmentDefinition",VARIABLE:"Variable",INT:"IntValue",FLOAT:"FloatValue",STRING:"StringValue",BOOLEAN:"BooleanValue",NULL:"NullValue",ENUM:"EnumValue",LIST:"ListValue",OBJECT:"ObjectValue",OBJECT_FIELD:"ObjectField",DIRECTIVE:"Directive",NAMED_TYPE:"NamedType",LIST_TYPE:"ListType",NON_NULL_TYPE:"NonNullType",SCHEMA_DEFINITION:"SchemaDefinition",OPERATION_TYPE_DEFINITION:"OperationTypeDefinition",SCALAR_TYPE_DEFINITION:"ScalarTypeDefinition",OBJECT_TYPE_DEFINITION:"ObjectTypeDefinition",FIELD_DEFINITION:"FieldDefinition",INPUT_VALUE_DEFINITION:"InputValueDefinition",INTERFACE_TYPE_DEFINITION:"InterfaceTypeDefinition",UNION_TYPE_DEFINITION:"UnionTypeDefinition",ENUM_TYPE_DEFINITION:"EnumTypeDefinition",ENUM_VALUE_DEFINITION:"EnumValueDefinition",INPUT_OBJECT_TYPE_DEFINITION:"InputObjectTypeDefinition",DIRECTIVE_DEFINITION:"DirectiveDefinition",SCHEMA_EXTENSION:"SchemaExtension",SCALAR_TYPE_EXTENSION:"ScalarTypeExtension",OBJECT_TYPE_EXTENSION:"ObjectTypeExtension",INTERFACE_TYPE_EXTENSION:"InterfaceTypeExtension",UNION_TYPE_EXTENSION:"UnionTypeExtension",ENUM_TYPE_EXTENSION:"EnumTypeExtension",INPUT_OBJECT_TYPE_EXTENSION:"InputObjectTypeExtension"});l.Kind=T}}),pe=K({"node_modules/graphql/jsutils/invariant.js"(l){"use strict";L(),Object.defineProperty(l,"__esModule",{value:!0}),l.default=T;function T(a,p){var r=Boolean(a);if(!r)throw new Error(p!=null?p:"Unexpected invariant triggered.")}}}),Z=K({"node_modules/graphql/jsutils/nodejsCustomInspectSymbol.js"(l){"use strict";L(),Object.defineProperty(l,"__esModule",{value:!0}),l.default=void 0;var T=typeof Symbol=="function"&&typeof Symbol.for=="function"?Symbol.for("nodejs.util.inspect.custom"):void 0,a=T;l.default=a}}),fe=K({"node_modules/graphql/jsutils/defineInspect.js"(l){"use strict";L(),Object.defineProperty(l,"__esModule",{value:!0}),l.default=r;var T=p(pe()),a=p(Z());function p(_){return _&&_.__esModule?_:{default:_}}function r(_){var E=_.prototype.toJSON;typeof E=="function"||(0,T.default)(0),_.prototype.inspect=E,a.default&&(_.prototype[a.default]=E)}}}),ee=K({"node_modules/graphql/language/ast.js"(l){"use strict";L(),Object.defineProperty(l,"__esModule",{value:!0}),l.isNode=_,l.Token=l.Location=void 0;var T=a(fe());function a(E){return E&&E.__esModule?E:{default:E}}var p=function(){function E(g,D,O){this.start=g.start,this.end=D.end,this.startToken=g,this.endToken=D,this.source=O}var k=E.prototype;return k.toJSON=function(){return{start:this.start,end:this.end}},E}();l.Location=p,(0,T.default)(p);var r=function(){function E(g,D,O,S,I,h,N){this.kind=g,this.start=D,this.end=O,this.line=S,this.column=I,this.value=N,this.prev=h,this.next=null}var k=E.prototype;return k.toJSON=function(){return{kind:this.kind,value:this.value,line:this.line,column:this.column}},E}();l.Token=r,(0,T.default)(r);function _(E){return E!=null&&typeof E.kind=="string"}}}),te=K({"node_modules/graphql/language/tokenKind.js"(l){"use strict";L(),Object.defineProperty(l,"__esModule",{value:!0}),l.TokenKind=void 0;var T=Object.freeze({SOF:"<SOF>",EOF:"<EOF>",BANG:"!",DOLLAR:"$",AMP:"&",PAREN_L:"(",PAREN_R:")",SPREAD:"...",COLON:":",EQUALS:"=",AT:"@",BRACKET_L:"[",BRACKET_R:"]",BRACE_L:"{",PIPE:"|",BRACE_R:"}",NAME:"Name",INT:"Int",FLOAT:"Float",STRING:"String",BLOCK_STRING:"BlockString",COMMENT:"Comment"});l.TokenKind=T}}),ne=K({"node_modules/graphql/jsutils/inspect.js"(l){"use strict";L(),Object.defineProperty(l,"__esModule",{value:!0}),l.default=E;var T=a(Z());function a(h){return h&&h.__esModule?h:{default:h}}function p(h){return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?p=function(i){return typeof i}:p=function(i){return i&&typeof Symbol=="function"&&i.constructor===Symbol&&i!==Symbol.prototype?"symbol":typeof i},p(h)}var r=10,_=2;function E(h){return k(h,[])}function k(h,N){switch(p(h)){case"string":return JSON.stringify(h);case"function":return h.name?"[function ".concat(h.name,"]"):"[function]";case"object":return h===null?"null":g(h,N);default:return String(h)}}function g(h,N){if(N.indexOf(h)!==-1)return"[Circular]";var i=[].concat(N,[h]),u=S(h);if(u!==void 0){var e=u.call(h);if(e!==h)return typeof e=="string"?e:k(e,i)}else if(Array.isArray(h))return O(h,i);return D(h,i)}function D(h,N){var i=Object.keys(h);if(i.length===0)return"{}";if(N.length>_)return"["+I(h)+"]";var u=i.map(function(e){var n=k(h[e],N);return e+": "+n});return"{ "+u.join(", ")+" }"}function O(h,N){if(h.length===0)return"[]";if(N.length>_)return"[Array]";for(var i=Math.min(r,h.length),u=h.length-i,e=[],n=0;n<i;++n)e.push(k(h[n],N));return u===1?e.push("... 1 more item"):u>1&&e.push("... ".concat(u," more items")),"["+e.join(", ")+"]"}function S(h){var N=h[String(T.default)];if(typeof N=="function")return N;if(typeof h.inspect=="function")return h.inspect}function I(h){var N=Object.prototype.toString.call(h).replace(/^\[object /,"").replace(/]$/,"");if(N==="Object"&&typeof h.constructor=="function"){var i=h.constructor.name;if(typeof i=="string"&&i!=="")return i}return N}}}),de=K({"node_modules/graphql/jsutils/devAssert.js"(l){"use strict";L(),Object.defineProperty(l,"__esModule",{value:!0}),l.default=T;function T(a,p){var r=Boolean(a);if(!r)throw new Error(p)}}}),he=K({"node_modules/graphql/jsutils/instanceOf.js"(l){"use strict";L(),Object.defineProperty(l,"__esModule",{value:!0}),l.default=void 0;var T=a(ne());function a(r){return r&&r.__esModule?r:{default:r}}var p=function(_,E){return _ instanceof E};l.default=p}}),ve=K({"node_modules/graphql/language/source.js"(l){"use strict";L(),Object.defineProperty(l,"__esModule",{value:!0}),l.isSource=D,l.Source=void 0;var T=H(),a=_(ne()),p=_(de()),r=_(he());function _(O){return O&&O.__esModule?O:{default:O}}function E(O,S){for(var I=0;I<S.length;I++){var h=S[I];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),Object.defineProperty(O,h.key,h)}}function k(O,S,I){return S&&E(O.prototype,S),I&&E(O,I),O}var g=function(){function O(S){var I=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"GraphQL request",h=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{line:1,column:1};typeof S=="string"||(0,p.default)(0,"Body must be a string. Received: ".concat((0,a.default)(S),".")),this.body=S,this.name=I,this.locationOffset=h,this.locationOffset.line>0||(0,p.default)(0,"line in locationOffset is 1-indexed and must be positive."),this.locationOffset.column>0||(0,p.default)(0,"column in locationOffset is 1-indexed and must be positive.")}return k(O,[{key:T.SYMBOL_TO_STRING_TAG,get:function(){return"Source"}}]),O}();l.Source=g;function D(O){return(0,r.default)(O,g)}}}),Te=K({"node_modules/graphql/language/directiveLocation.js"(l){"use strict";L(),Object.defineProperty(l,"__esModule",{value:!0}),l.DirectiveLocation=void 0;var T=Object.freeze({QUERY:"QUERY",MUTATION:"MUTATION",SUBSCRIPTION:"SUBSCRIPTION",FIELD:"FIELD",FRAGMENT_DEFINITION:"FRAGMENT_DEFINITION",FRAGMENT_SPREAD:"FRAGMENT_SPREAD",INLINE_FRAGMENT:"INLINE_FRAGMENT",VARIABLE_DEFINITION:"VARIABLE_DEFINITION",SCHEMA:"SCHEMA",SCALAR:"SCALAR",OBJECT:"OBJECT",FIELD_DEFINITION:"FIELD_DEFINITION",ARGUMENT_DEFINITION:"ARGUMENT_DEFINITION",INTERFACE:"INTERFACE",UNION:"UNION",ENUM:"ENUM",ENUM_VALUE:"ENUM_VALUE",INPUT_OBJECT:"INPUT_OBJECT",INPUT_FIELD_DEFINITION:"INPUT_FIELD_DEFINITION"});l.DirectiveLocation=T}}),_e=K({"node_modules/graphql/language/blockString.js"(l){"use strict";L(),Object.defineProperty(l,"__esModule",{value:!0}),l.dedentBlockStringValue=T,l.getBlockStringIndentation=p,l.printBlockString=r;function T(_){var E=_.split(/\r\n|[\n\r]/g),k=p(_);if(k!==0)for(var g=1;g<E.length;g++)E[g]=E[g].slice(k);for(var D=0;D<E.length&&a(E[D]);)++D;for(var O=E.length;O>D&&a(E[O-1]);)--O;return E.slice(D,O).join(`
`)}function a(_){for(var E=0;E<_.length;++E)if(_[E]!==" "&&_[E]!=="	")return!1;return!0}function p(_){for(var E,k=!0,g=!0,D=0,O=null,S=0;S<_.length;++S)switch(_.charCodeAt(S)){case 13:_.charCodeAt(S+1)===10&&++S;case 10:k=!1,g=!0,D=0;break;case 9:case 32:++D;break;default:g&&!k&&(O===null||D<O)&&(O=D),g=!1}return(E=O)!==null&&E!==void 0?E:0}function r(_){var E=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"",k=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,g=_.indexOf(`
`)===-1,D=_[0]===" "||_[0]==="	",O=_[_.length-1]==='"',S=_[_.length-1]==="\\",I=!g||O||S||k,h="";return I&&!(g&&D)&&(h+=`
`+E),h+=E?_.replace(/\n/g,`
`+E):_,I&&(h+=`
`),'"""'+h.replace(/"""/g,'\\"""')+'"""'}}}),Ee=K({"node_modules/graphql/language/lexer.js"(l){"use strict";L(),Object.defineProperty(l,"__esModule",{value:!0}),l.isPunctuatorTokenKind=E,l.Lexer=void 0;var T=W(),a=ee(),p=te(),r=_e(),_=function(){function t(y){var f=new a.Token(p.TokenKind.SOF,0,0,0,0,null);this.source=y,this.lastToken=f,this.token=f,this.line=1,this.lineStart=0}var s=t.prototype;return s.advance=function(){this.lastToken=this.token;var f=this.token=this.lookahead();return f},s.lookahead=function(){var f=this.token;if(f.kind!==p.TokenKind.EOF)do{var m;f=(m=f.next)!==null&&m!==void 0?m:f.next=g(this,f)}while(f.kind===p.TokenKind.COMMENT);return f},t}();l.Lexer=_;function E(t){return t===p.TokenKind.BANG||t===p.TokenKind.DOLLAR||t===p.TokenKind.AMP||t===p.TokenKind.PAREN_L||t===p.TokenKind.PAREN_R||t===p.TokenKind.SPREAD||t===p.TokenKind.COLON||t===p.TokenKind.EQUALS||t===p.TokenKind.AT||t===p.TokenKind.BRACKET_L||t===p.TokenKind.BRACKET_R||t===p.TokenKind.BRACE_L||t===p.TokenKind.PIPE||t===p.TokenKind.BRACE_R}function k(t){return isNaN(t)?p.TokenKind.EOF:t<127?JSON.stringify(String.fromCharCode(t)):'"\\u'.concat(("00"+t.toString(16).toUpperCase()).slice(-4),'"')}function g(t,s){for(var y=t.source,f=y.body,m=f.length,o=s.end;o<m;){var d=f.charCodeAt(o),c=t.line,v=1+o-t.lineStart;switch(d){case 65279:case 9:case 32:case 44:++o;continue;case 10:++o,++t.line,t.lineStart=o;continue;case 13:f.charCodeAt(o+1)===10?o+=2:++o,++t.line,t.lineStart=o;continue;case 33:return new a.Token(p.TokenKind.BANG,o,o+1,c,v,s);case 35:return O(y,o,c,v,s);case 36:return new a.Token(p.TokenKind.DOLLAR,o,o+1,c,v,s);case 38:return new a.Token(p.TokenKind.AMP,o,o+1,c,v,s);case 40:return new a.Token(p.TokenKind.PAREN_L,o,o+1,c,v,s);case 41:return new a.Token(p.TokenKind.PAREN_R,o,o+1,c,v,s);case 46:if(f.charCodeAt(o+1)===46&&f.charCodeAt(o+2)===46)return new a.Token(p.TokenKind.SPREAD,o,o+3,c,v,s);break;case 58:return new a.Token(p.TokenKind.COLON,o,o+1,c,v,s);case 61:return new a.Token(p.TokenKind.EQUALS,o,o+1,c,v,s);case 64:return new a.Token(p.TokenKind.AT,o,o+1,c,v,s);case 91:return new a.Token(p.TokenKind.BRACKET_L,o,o+1,c,v,s);case 93:return new a.Token(p.TokenKind.BRACKET_R,o,o+1,c,v,s);case 123:return new a.Token(p.TokenKind.BRACE_L,o,o+1,c,v,s);case 124:return new a.Token(p.TokenKind.PIPE,o,o+1,c,v,s);case 125:return new a.Token(p.TokenKind.BRACE_R,o,o+1,c,v,s);case 34:return f.charCodeAt(o+1)===34&&f.charCodeAt(o+2)===34?N(y,o,c,v,s,t):h(y,o,c,v,s);case 45:case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return S(y,o,d,c,v,s);case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 95:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:return e(y,o,c,v,s)}throw(0,T.syntaxError)(y,o,D(d))}var A=t.line,x=1+o-t.lineStart;return new a.Token(p.TokenKind.EOF,m,m,A,x,s)}function D(t){return t<32&&t!==9&&t!==10&&t!==13?"Cannot contain the invalid character ".concat(k(t),"."):t===39?`Unexpected single quote character ('), did you mean to use a double quote (")?`:"Cannot parse the unexpected character ".concat(k(t),".")}function O(t,s,y,f,m){var o=t.body,d,c=s;do d=o.charCodeAt(++c);while(!isNaN(d)&&(d>31||d===9));return new a.Token(p.TokenKind.COMMENT,s,c,y,f,m,o.slice(s+1,c))}function S(t,s,y,f,m,o){var d=t.body,c=y,v=s,A=!1;if(c===45&&(c=d.charCodeAt(++v)),c===48){if(c=d.charCodeAt(++v),c>=48&&c<=57)throw(0,T.syntaxError)(t,v,"Invalid number, unexpected digit after 0: ".concat(k(c),"."))}else v=I(t,v,c),c=d.charCodeAt(v);if(c===46&&(A=!0,c=d.charCodeAt(++v),v=I(t,v,c),c=d.charCodeAt(v)),(c===69||c===101)&&(A=!0,c=d.charCodeAt(++v),(c===43||c===45)&&(c=d.charCodeAt(++v)),v=I(t,v,c),c=d.charCodeAt(v)),c===46||n(c))throw(0,T.syntaxError)(t,v,"Invalid number, expected digit but got: ".concat(k(c),"."));return new a.Token(A?p.TokenKind.FLOAT:p.TokenKind.INT,s,v,f,m,o,d.slice(s,v))}function I(t,s,y){var f=t.body,m=s,o=y;if(o>=48&&o<=57){do o=f.charCodeAt(++m);while(o>=48&&o<=57);return m}throw(0,T.syntaxError)(t,m,"Invalid number, expected digit but got: ".concat(k(o),"."))}function h(t,s,y,f,m){for(var o=t.body,d=s+1,c=d,v=0,A="";d<o.length&&!isNaN(v=o.charCodeAt(d))&&v!==10&&v!==13;){if(v===34)return A+=o.slice(c,d),new a.Token(p.TokenKind.STRING,s,d+1,y,f,m,A);if(v<32&&v!==9)throw(0,T.syntaxError)(t,d,"Invalid character within String: ".concat(k(v),"."));if(++d,v===92){switch(A+=o.slice(c,d-1),v=o.charCodeAt(d),v){case 34:A+='"';break;case 47:A+="/";break;case 92:A+="\\";break;case 98:A+="\b";break;case 102:A+="\f";break;case 110:A+=`
`;break;case 114:A+="\r";break;case 116:A+="	";break;case 117:{var x=i(o.charCodeAt(d+1),o.charCodeAt(d+2),o.charCodeAt(d+3),o.charCodeAt(d+4));if(x<0){var b=o.slice(d+1,d+5);throw(0,T.syntaxError)(t,d,"Invalid character escape sequence: \\u".concat(b,"."))}A+=String.fromCharCode(x),d+=4;break}default:throw(0,T.syntaxError)(t,d,"Invalid character escape sequence: \\".concat(String.fromCharCode(v),"."))}++d,c=d}}throw(0,T.syntaxError)(t,d,"Unterminated string.")}function N(t,s,y,f,m,o){for(var d=t.body,c=s+3,v=c,A=0,x="";c<d.length&&!isNaN(A=d.charCodeAt(c));){if(A===34&&d.charCodeAt(c+1)===34&&d.charCodeAt(c+2)===34)return x+=d.slice(v,c),new a.Token(p.TokenKind.BLOCK_STRING,s,c+3,y,f,m,(0,r.dedentBlockStringValue)(x));if(A<32&&A!==9&&A!==10&&A!==13)throw(0,T.syntaxError)(t,c,"Invalid character within String: ".concat(k(A),"."));A===10?(++c,++o.line,o.lineStart=c):A===13?(d.charCodeAt(c+1)===10?c+=2:++c,++o.line,o.lineStart=c):A===92&&d.charCodeAt(c+1)===34&&d.charCodeAt(c+2)===34&&d.charCodeAt(c+3)===34?(x+=d.slice(v,c)+'"""',c+=4,v=c):++c}throw(0,T.syntaxError)(t,c,"Unterminated string.")}function i(t,s,y,f){return u(t)<<12|u(s)<<8|u(y)<<4|u(f)}function u(t){return t>=48&&t<=57?t-48:t>=65&&t<=70?t-55:t>=97&&t<=102?t-87:-1}function e(t,s,y,f,m){for(var o=t.body,d=o.length,c=s+1,v=0;c!==d&&!isNaN(v=o.charCodeAt(c))&&(v===95||v>=48&&v<=57||v>=65&&v<=90||v>=97&&v<=122);)++c;return new a.Token(p.TokenKind.NAME,s,c,y,f,m,o.slice(s,c))}function n(t){return t===95||t>=65&&t<=90||t>=97&&t<=122}}}),me=K({"node_modules/graphql/language/parser.js"(l){"use strict";L(),Object.defineProperty(l,"__esModule",{value:!0}),l.parse=g,l.parseValue=D,l.parseType=O,l.Parser=void 0;var T=W(),a=le(),p=ee(),r=te(),_=ve(),E=Te(),k=Ee();function g(N,i){var u=new S(N,i);return u.parseDocument()}function D(N,i){var u=new S(N,i);u.expectToken(r.TokenKind.SOF);var e=u.parseValueLiteral(!1);return u.expectToken(r.TokenKind.EOF),e}function O(N,i){var u=new S(N,i);u.expectToken(r.TokenKind.SOF);var e=u.parseTypeReference();return u.expectToken(r.TokenKind.EOF),e}var S=function(){function N(u,e){var n=(0,_.isSource)(u)?u:new _.Source(u);this._lexer=new k.Lexer(n),this._options=e}var i=N.prototype;return i.parseName=function(){var e=this.expectToken(r.TokenKind.NAME);return{kind:a.Kind.NAME,value:e.value,loc:this.loc(e)}},i.parseDocument=function(){var e=this._lexer.token;return{kind:a.Kind.DOCUMENT,definitions:this.many(r.TokenKind.SOF,this.parseDefinition,r.TokenKind.EOF),loc:this.loc(e)}},i.parseDefinition=function(){if(this.peek(r.TokenKind.NAME))switch(this._lexer.token.value){case"query":case"mutation":case"subscription":return this.parseOperationDefinition();case"fragment":return this.parseFragmentDefinition();case"schema":case"scalar":case"type":case"interface":case"union":case"enum":case"input":case"directive":return this.parseTypeSystemDefinition();case"extend":return this.parseTypeSystemExtension()}else{if(this.peek(r.TokenKind.BRACE_L))return this.parseOperationDefinition();if(this.peekDescription())return this.parseTypeSystemDefinition()}throw this.unexpected()},i.parseOperationDefinition=function(){var e=this._lexer.token;if(this.peek(r.TokenKind.BRACE_L))return{kind:a.Kind.OPERATION_DEFINITION,operation:"query",name:void 0,variableDefinitions:[],directives:[],selectionSet:this.parseSelectionSet(),loc:this.loc(e)};var n=this.parseOperationType(),t;return this.peek(r.TokenKind.NAME)&&(t=this.parseName()),{kind:a.Kind.OPERATION_DEFINITION,operation:n,name:t,variableDefinitions:this.parseVariableDefinitions(),directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet(),loc:this.loc(e)}},i.parseOperationType=function(){var e=this.expectToken(r.TokenKind.NAME);switch(e.value){case"query":return"query";case"mutation":return"mutation";case"subscription":return"subscription"}throw this.unexpected(e)},i.parseVariableDefinitions=function(){return this.optionalMany(r.TokenKind.PAREN_L,this.parseVariableDefinition,r.TokenKind.PAREN_R)},i.parseVariableDefinition=function(){var e=this._lexer.token;return{kind:a.Kind.VARIABLE_DEFINITION,variable:this.parseVariable(),type:(this.expectToken(r.TokenKind.COLON),this.parseTypeReference()),defaultValue:this.expectOptionalToken(r.TokenKind.EQUALS)?this.parseValueLiteral(!0):void 0,directives:this.parseDirectives(!0),loc:this.loc(e)}},i.parseVariable=function(){var e=this._lexer.token;return this.expectToken(r.TokenKind.DOLLAR),{kind:a.Kind.VARIABLE,name:this.parseName(),loc:this.loc(e)}},i.parseSelectionSet=function(){var e=this._lexer.token;return{kind:a.Kind.SELECTION_SET,selections:this.many(r.TokenKind.BRACE_L,this.parseSelection,r.TokenKind.BRACE_R),loc:this.loc(e)}},i.parseSelection=function(){return this.peek(r.TokenKind.SPREAD)?this.parseFragment():this.parseField()},i.parseField=function(){var e=this._lexer.token,n=this.parseName(),t,s;return this.expectOptionalToken(r.TokenKind.COLON)?(t=n,s=this.parseName()):s=n,{kind:a.Kind.FIELD,alias:t,name:s,arguments:this.parseArguments(!1),directives:this.parseDirectives(!1),selectionSet:this.peek(r.TokenKind.BRACE_L)?this.parseSelectionSet():void 0,loc:this.loc(e)}},i.parseArguments=function(e){var n=e?this.parseConstArgument:this.parseArgument;return this.optionalMany(r.TokenKind.PAREN_L,n,r.TokenKind.PAREN_R)},i.parseArgument=function(){var e=this._lexer.token,n=this.parseName();return this.expectToken(r.TokenKind.COLON),{kind:a.Kind.ARGUMENT,name:n,value:this.parseValueLiteral(!1),loc:this.loc(e)}},i.parseConstArgument=function(){var e=this._lexer.token;return{kind:a.Kind.ARGUMENT,name:this.parseName(),value:(this.expectToken(r.TokenKind.COLON),this.parseValueLiteral(!0)),loc:this.loc(e)}},i.parseFragment=function(){var e=this._lexer.token;this.expectToken(r.TokenKind.SPREAD);var n=this.expectOptionalKeyword("on");return!n&&this.peek(r.TokenKind.NAME)?{kind:a.Kind.FRAGMENT_SPREAD,name:this.parseFragmentName(),directives:this.parseDirectives(!1),loc:this.loc(e)}:{kind:a.Kind.INLINE_FRAGMENT,typeCondition:n?this.parseNamedType():void 0,directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet(),loc:this.loc(e)}},i.parseFragmentDefinition=function(){var e,n=this._lexer.token;return this.expectKeyword("fragment"),((e=this._options)===null||e===void 0?void 0:e.experimentalFragmentVariables)===!0?{kind:a.Kind.FRAGMENT_DEFINITION,name:this.parseFragmentName(),variableDefinitions:this.parseVariableDefinitions(),typeCondition:(this.expectKeyword("on"),this.parseNamedType()),directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet(),loc:this.loc(n)}:{kind:a.Kind.FRAGMENT_DEFINITION,name:this.parseFragmentName(),typeCondition:(this.expectKeyword("on"),this.parseNamedType()),directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet(),loc:this.loc(n)}},i.parseFragmentName=function(){if(this._lexer.token.value==="on")throw this.unexpected();return this.parseName()},i.parseValueLiteral=function(e){var n=this._lexer.token;switch(n.kind){case r.TokenKind.BRACKET_L:return this.parseList(e);case r.TokenKind.BRACE_L:return this.parseObject(e);case r.TokenKind.INT:return this._lexer.advance(),{kind:a.Kind.INT,value:n.value,loc:this.loc(n)};case r.TokenKind.FLOAT:return this._lexer.advance(),{kind:a.Kind.FLOAT,value:n.value,loc:this.loc(n)};case r.TokenKind.STRING:case r.TokenKind.BLOCK_STRING:return this.parseStringLiteral();case r.TokenKind.NAME:switch(this._lexer.advance(),n.value){case"true":return{kind:a.Kind.BOOLEAN,value:!0,loc:this.loc(n)};case"false":return{kind:a.Kind.BOOLEAN,value:!1,loc:this.loc(n)};case"null":return{kind:a.Kind.NULL,loc:this.loc(n)};default:return{kind:a.Kind.ENUM,value:n.value,loc:this.loc(n)}}case r.TokenKind.DOLLAR:if(!e)return this.parseVariable();break}throw this.unexpected()},i.parseStringLiteral=function(){var e=this._lexer.token;return this._lexer.advance(),{kind:a.Kind.STRING,value:e.value,block:e.kind===r.TokenKind.BLOCK_STRING,loc:this.loc(e)}},i.parseList=function(e){var n=this,t=this._lexer.token,s=function(){return n.parseValueLiteral(e)};return{kind:a.Kind.LIST,values:this.any(r.TokenKind.BRACKET_L,s,r.TokenKind.BRACKET_R),loc:this.loc(t)}},i.parseObject=function(e){var n=this,t=this._lexer.token,s=function(){return n.parseObjectField(e)};return{kind:a.Kind.OBJECT,fields:this.any(r.TokenKind.BRACE_L,s,r.TokenKind.BRACE_R),loc:this.loc(t)}},i.parseObjectField=function(e){var n=this._lexer.token,t=this.parseName();return this.expectToken(r.TokenKind.COLON),{kind:a.Kind.OBJECT_FIELD,name:t,value:this.parseValueLiteral(e),loc:this.loc(n)}},i.parseDirectives=function(e){for(var n=[];this.peek(r.TokenKind.AT);)n.push(this.parseDirective(e));return n},i.parseDirective=function(e){var n=this._lexer.token;return this.expectToken(r.TokenKind.AT),{kind:a.Kind.DIRECTIVE,name:this.parseName(),arguments:this.parseArguments(e),loc:this.loc(n)}},i.parseTypeReference=function(){var e=this._lexer.token,n;return this.expectOptionalToken(r.TokenKind.BRACKET_L)?(n=this.parseTypeReference(),this.expectToken(r.TokenKind.BRACKET_R),n={kind:a.Kind.LIST_TYPE,type:n,loc:this.loc(e)}):n=this.parseNamedType(),this.expectOptionalToken(r.TokenKind.BANG)?{kind:a.Kind.NON_NULL_TYPE,type:n,loc:this.loc(e)}:n},i.parseNamedType=function(){var e=this._lexer.token;return{kind:a.Kind.NAMED_TYPE,name:this.parseName(),loc:this.loc(e)}},i.parseTypeSystemDefinition=function(){var e=this.peekDescription()?this._lexer.lookahead():this._lexer.token;if(e.kind===r.TokenKind.NAME)switch(e.value){case"schema":return this.parseSchemaDefinition();case"scalar":return this.parseScalarTypeDefinition();case"type":return this.parseObjectTypeDefinition();case"interface":return this.parseInterfaceTypeDefinition();case"union":return this.parseUnionTypeDefinition();case"enum":return this.parseEnumTypeDefinition();case"input":return this.parseInputObjectTypeDefinition();case"directive":return this.parseDirectiveDefinition()}throw this.unexpected(e)},i.peekDescription=function(){return this.peek(r.TokenKind.STRING)||this.peek(r.TokenKind.BLOCK_STRING)},i.parseDescription=function(){if(this.peekDescription())return this.parseStringLiteral()},i.parseSchemaDefinition=function(){var e=this._lexer.token,n=this.parseDescription();this.expectKeyword("schema");var t=this.parseDirectives(!0),s=this.many(r.TokenKind.BRACE_L,this.parseOperationTypeDefinition,r.TokenKind.BRACE_R);return{kind:a.Kind.SCHEMA_DEFINITION,description:n,directives:t,operationTypes:s,loc:this.loc(e)}},i.parseOperationTypeDefinition=function(){var e=this._lexer.token,n=this.parseOperationType();this.expectToken(r.TokenKind.COLON);var t=this.parseNamedType();return{kind:a.Kind.OPERATION_TYPE_DEFINITION,operation:n,type:t,loc:this.loc(e)}},i.parseScalarTypeDefinition=function(){var e=this._lexer.token,n=this.parseDescription();this.expectKeyword("scalar");var t=this.parseName(),s=this.parseDirectives(!0);return{kind:a.Kind.SCALAR_TYPE_DEFINITION,description:n,name:t,directives:s,loc:this.loc(e)}},i.parseObjectTypeDefinition=function(){var e=this._lexer.token,n=this.parseDescription();this.expectKeyword("type");var t=this.parseName(),s=this.parseImplementsInterfaces(),y=this.parseDirectives(!0),f=this.parseFieldsDefinition();return{kind:a.Kind.OBJECT_TYPE_DEFINITION,description:n,name:t,interfaces:s,directives:y,fields:f,loc:this.loc(e)}},i.parseImplementsInterfaces=function(){var e;if(!this.expectOptionalKeyword("implements"))return[];if(((e=this._options)===null||e===void 0?void 0:e.allowLegacySDLImplementsInterfaces)===!0){var n=[];this.expectOptionalToken(r.TokenKind.AMP);do n.push(this.parseNamedType());while(this.expectOptionalToken(r.TokenKind.AMP)||this.peek(r.TokenKind.NAME));return n}return this.delimitedMany(r.TokenKind.AMP,this.parseNamedType)},i.parseFieldsDefinition=function(){var e;return((e=this._options)===null||e===void 0?void 0:e.allowLegacySDLEmptyFields)===!0&&this.peek(r.TokenKind.BRACE_L)&&this._lexer.lookahead().kind===r.TokenKind.BRACE_R?(this._lexer.advance(),this._lexer.advance(),[]):this.optionalMany(r.TokenKind.BRACE_L,this.parseFieldDefinition,r.TokenKind.BRACE_R)},i.parseFieldDefinition=function(){var e=this._lexer.token,n=this.parseDescription(),t=this.parseName(),s=this.parseArgumentDefs();this.expectToken(r.TokenKind.COLON);var y=this.parseTypeReference(),f=this.parseDirectives(!0);return{kind:a.Kind.FIELD_DEFINITION,description:n,name:t,arguments:s,type:y,directives:f,loc:this.loc(e)}},i.parseArgumentDefs=function(){return this.optionalMany(r.TokenKind.PAREN_L,this.parseInputValueDef,r.TokenKind.PAREN_R)},i.parseInputValueDef=function(){var e=this._lexer.token,n=this.parseDescription(),t=this.parseName();this.expectToken(r.TokenKind.COLON);var s=this.parseTypeReference(),y;this.expectOptionalToken(r.TokenKind.EQUALS)&&(y=this.parseValueLiteral(!0));var f=this.parseDirectives(!0);return{kind:a.Kind.INPUT_VALUE_DEFINITION,description:n,name:t,type:s,defaultValue:y,directives:f,loc:this.loc(e)}},i.parseInterfaceTypeDefinition=function(){var e=this._lexer.token,n=this.parseDescription();this.expectKeyword("interface");var t=this.parseName(),s=this.parseImplementsInterfaces(),y=this.parseDirectives(!0),f=this.parseFieldsDefinition();return{kind:a.Kind.INTERFACE_TYPE_DEFINITION,description:n,name:t,interfaces:s,directives:y,fields:f,loc:this.loc(e)}},i.parseUnionTypeDefinition=function(){var e=this._lexer.token,n=this.parseDescription();this.expectKeyword("union");var t=this.parseName(),s=this.parseDirectives(!0),y=this.parseUnionMemberTypes();return{kind:a.Kind.UNION_TYPE_DEFINITION,description:n,name:t,directives:s,types:y,loc:this.loc(e)}},i.parseUnionMemberTypes=function(){return this.expectOptionalToken(r.TokenKind.EQUALS)?this.delimitedMany(r.TokenKind.PIPE,this.parseNamedType):[]},i.parseEnumTypeDefinition=function(){var e=this._lexer.token,n=this.parseDescription();this.expectKeyword("enum");var t=this.parseName(),s=this.parseDirectives(!0),y=this.parseEnumValuesDefinition();return{kind:a.Kind.ENUM_TYPE_DEFINITION,description:n,name:t,directives:s,values:y,loc:this.loc(e)}},i.parseEnumValuesDefinition=function(){return this.optionalMany(r.TokenKind.BRACE_L,this.parseEnumValueDefinition,r.TokenKind.BRACE_R)},i.parseEnumValueDefinition=function(){var e=this._lexer.token,n=this.parseDescription(),t=this.parseName(),s=this.parseDirectives(!0);return{kind:a.Kind.ENUM_VALUE_DEFINITION,description:n,name:t,directives:s,loc:this.loc(e)}},i.parseInputObjectTypeDefinition=function(){var e=this._lexer.token,n=this.parseDescription();this.expectKeyword("input");var t=this.parseName(),s=this.parseDirectives(!0),y=this.parseInputFieldsDefinition();return{kind:a.Kind.INPUT_OBJECT_TYPE_DEFINITION,description:n,name:t,directives:s,fields:y,loc:this.loc(e)}},i.parseInputFieldsDefinition=function(){return this.optionalMany(r.TokenKind.BRACE_L,this.parseInputValueDef,r.TokenKind.BRACE_R)},i.parseTypeSystemExtension=function(){var e=this._lexer.lookahead();if(e.kind===r.TokenKind.NAME)switch(e.value){case"schema":return this.parseSchemaExtension();case"scalar":return this.parseScalarTypeExtension();case"type":return this.parseObjectTypeExtension();case"interface":return this.parseInterfaceTypeExtension();case"union":return this.parseUnionTypeExtension();case"enum":return this.parseEnumTypeExtension();case"input":return this.parseInputObjectTypeExtension()}throw this.unexpected(e)},i.parseSchemaExtension=function(){var e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("schema");var n=this.parseDirectives(!0),t=this.optionalMany(r.TokenKind.BRACE_L,this.parseOperationTypeDefinition,r.TokenKind.BRACE_R);if(n.length===0&&t.length===0)throw this.unexpected();return{kind:a.Kind.SCHEMA_EXTENSION,directives:n,operationTypes:t,loc:this.loc(e)}},i.parseScalarTypeExtension=function(){var e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("scalar");var n=this.parseName(),t=this.parseDirectives(!0);if(t.length===0)throw this.unexpected();return{kind:a.Kind.SCALAR_TYPE_EXTENSION,name:n,directives:t,loc:this.loc(e)}},i.parseObjectTypeExtension=function(){var e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("type");var n=this.parseName(),t=this.parseImplementsInterfaces(),s=this.parseDirectives(!0),y=this.parseFieldsDefinition();if(t.length===0&&s.length===0&&y.length===0)throw this.unexpected();return{kind:a.Kind.OBJECT_TYPE_EXTENSION,name:n,interfaces:t,directives:s,fields:y,loc:this.loc(e)}},i.parseInterfaceTypeExtension=function(){var e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("interface");var n=this.parseName(),t=this.parseImplementsInterfaces(),s=this.parseDirectives(!0),y=this.parseFieldsDefinition();if(t.length===0&&s.length===0&&y.length===0)throw this.unexpected();return{kind:a.Kind.INTERFACE_TYPE_EXTENSION,name:n,interfaces:t,directives:s,fields:y,loc:this.loc(e)}},i.parseUnionTypeExtension=function(){var e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("union");var n=this.parseName(),t=this.parseDirectives(!0),s=this.parseUnionMemberTypes();if(t.length===0&&s.length===0)throw this.unexpected();return{kind:a.Kind.UNION_TYPE_EXTENSION,name:n,directives:t,types:s,loc:this.loc(e)}},i.parseEnumTypeExtension=function(){var e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("enum");var n=this.parseName(),t=this.parseDirectives(!0),s=this.parseEnumValuesDefinition();if(t.length===0&&s.length===0)throw this.unexpected();return{kind:a.Kind.ENUM_TYPE_EXTENSION,name:n,directives:t,values:s,loc:this.loc(e)}},i.parseInputObjectTypeExtension=function(){var e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("input");var n=this.parseName(),t=this.parseDirectives(!0),s=this.parseInputFieldsDefinition();if(t.length===0&&s.length===0)throw this.unexpected();return{kind:a.Kind.INPUT_OBJECT_TYPE_EXTENSION,name:n,directives:t,fields:s,loc:this.loc(e)}},i.parseDirectiveDefinition=function(){var e=this._lexer.token,n=this.parseDescription();this.expectKeyword("directive"),this.expectToken(r.TokenKind.AT);var t=this.parseName(),s=this.parseArgumentDefs(),y=this.expectOptionalKeyword("repeatable");this.expectKeyword("on");var f=this.parseDirectiveLocations();return{kind:a.Kind.DIRECTIVE_DEFINITION,description:n,name:t,arguments:s,repeatable:y,locations:f,loc:this.loc(e)}},i.parseDirectiveLocations=function(){return this.delimitedMany(r.TokenKind.PIPE,this.parseDirectiveLocation)},i.parseDirectiveLocation=function(){var e=this._lexer.token,n=this.parseName();if(E.DirectiveLocation[n.value]!==void 0)return n;throw this.unexpected(e)},i.loc=function(e){var n;if(((n=this._options)===null||n===void 0?void 0:n.noLocation)!==!0)return new p.Location(e,this._lexer.lastToken,this._lexer.source)},i.peek=function(e){return this._lexer.token.kind===e},i.expectToken=function(e){var n=this._lexer.token;if(n.kind===e)return this._lexer.advance(),n;throw(0,T.syntaxError)(this._lexer.source,n.start,"Expected ".concat(h(e),", found ").concat(I(n),"."))},i.expectOptionalToken=function(e){var n=this._lexer.token;if(n.kind===e)return this._lexer.advance(),n},i.expectKeyword=function(e){var n=this._lexer.token;if(n.kind===r.TokenKind.NAME&&n.value===e)this._lexer.advance();else throw(0,T.syntaxError)(this._lexer.source,n.start,'Expected "'.concat(e,'", found ').concat(I(n),"."))},i.expectOptionalKeyword=function(e){var n=this._lexer.token;return n.kind===r.TokenKind.NAME&&n.value===e?(this._lexer.advance(),!0):!1},i.unexpected=function(e){var n=e!=null?e:this._lexer.token;return(0,T.syntaxError)(this._lexer.source,n.start,"Unexpected ".concat(I(n),"."))},i.any=function(e,n,t){this.expectToken(e);for(var s=[];!this.expectOptionalToken(t);)s.push(n.call(this));return s},i.optionalMany=function(e,n,t){if(this.expectOptionalToken(e)){var s=[];do s.push(n.call(this));while(!this.expectOptionalToken(t));return s}return[]},i.many=function(e,n,t){this.expectToken(e);var s=[];do s.push(n.call(this));while(!this.expectOptionalToken(t));return s},i.delimitedMany=function(e,n){this.expectOptionalToken(e);var t=[];do t.push(n.call(this));while(this.expectOptionalToken(e));return t},N}();l.Parser=S;function I(N){var i=N.value;return h(N.kind)+(i!=null?' "'.concat(i,'"'):"")}function h(N){return(0,k.isPunctuatorTokenKind)(N)?'"'.concat(N,'"'):N}}}),ye=K({"src/language-graphql/parser-graphql.js"(l,T){L();var a=ie(),p=ae(),{hasPragma:r}=oe(),{locStart:_,locEnd:E}=se();function k(I){let h=[],{startToken:N}=I.loc,{next:i}=N;for(;i.kind!=="<EOF>";)i.kind==="Comment"&&(Object.assign(i,{column:i.column-1}),h.push(i)),i=i.next;return h}function g(I){if(I&&typeof I=="object"){delete I.startToken,delete I.endToken,delete I.prev,delete I.next;for(let h in I)g(I[h])}return I}var D={allowLegacySDLImplementsInterfaces:!1,experimentalFragmentVariables:!0};function O(I){let{GraphQLError:h}=$();if(I instanceof h){let{message:N,locations:[i]}=I;return a(N,{start:i})}return I}function S(I){let{parse:h}=me(),{result:N,error:i}=p(()=>h(I,Object.assign({},D)),()=>h(I,Object.assign(Object.assign({},D),{},{allowLegacySDLImplementsInterfaces:!0})));if(!N)throw O(i);return N.comments=k(N),g(N),N}T.exports={parsers:{graphql:{parse:S,astFormat:"graphql",hasPragma:r,locStart:_,locEnd:E}}}}}),ke=ye();export{ke as default};
