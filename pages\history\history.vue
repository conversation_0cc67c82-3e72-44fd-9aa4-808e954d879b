<template>
  <view class="container">
    <!-- 顶部标题 -->
    <view class="header">
      <text class="title">历史记录</text>
      <text class="subtitle">详细的收支记录查看</text>
    </view>

    <!-- 筛选选项 -->
    <view class="filter-section">
      <view class="filter-tabs">
        <view
          :class="['filter-tab', filterType === 'all' ? 'active' : '']"
          @click="setFilter('all')"
        >
          <text class="tab-text">全部</text>
        </view>
        <view
          :class="['filter-tab', filterType === 'income' ? 'active income' : '']"
          @click="setFilter('income')"
        >
          <text class="tab-text">收入</text>
        </view>
        <view
          :class="['filter-tab', filterType === 'expense' ? 'active expense' : '']"
          @click="setFilter('expense')"
        >
          <text class="tab-text">支出</text>
        </view>
      </view>
    </view>

    <!-- 统计概览 -->
    <view class="stats-overview">
      <view class="stats-card total-card">
        <view class="stats-icon">💰</view>
        <view class="stats-content">
          <text class="stats-label">总计</text>
          <text class="stats-amount" :class="totalBalance >= 0 ? 'positive' : 'negative'">
            {{ totalBalance >= 0 ? '+' : '' }}¥{{ Math.abs(totalBalance).toFixed(0) }}
          </text>
          <text class="stats-detail">{{ totalRecords }}笔记录</text>
        </view>
      </view>

      <view v-if="filterType === 'income' || filterType === 'all'" class="stats-card income-card">
        <view class="stats-icon">📈</view>
        <view class="stats-content">
          <text class="stats-label">总收入</text>
          <text class="stats-amount positive">¥{{ totalIncome.toFixed(0) }}</text>
          <text class="stats-detail">{{ incomeCount }}笔</text>
        </view>
      </view>

      <view v-if="filterType === 'expense' || filterType === 'all'" class="stats-card expense-card">
        <view class="stats-icon">📉</view>
        <view class="stats-content">
          <text class="stats-label">总支出</text>
          <text class="stats-amount negative">¥{{ totalExpense.toFixed(0) }}</text>
          <text class="stats-detail">{{ expenseCount }}笔</text>
        </view>
      </view>
    </view>

    <!-- 记录列表 -->
    <view v-if="filteredRecords.length > 0" class="records-section">
      <view 
        v-for="(group, date) in groupedRecords" 
        :key="date"
        class="date-group"
      >
        <!-- 日期标题 -->
        <view class="date-header">
          <text class="date-text large-text text-dark">{{ formatGroupDate(date) }}</text>
          <text class="date-amount large-text" :class="getDayAmountClass(group)">
            {{ getDayAmountText(group) }}
          </text>
        </view>
        
        <!-- 当日记录 -->
        <view class="records-card card">
          <view 
            v-for="record in group" 
            :key="record.id"
            class="record-item"
            @click="showRecordDetail(record)"
            @longpress="showRecordOptions(record)"
          >
            <view class="record-main">
              <view class="record-category large-text text-dark">
                {{ record.category }}
              </view>
              <view v-if="record.note" class="record-note text-gray">
                {{ record.note }}
              </view>
            </view>
            <view class="record-amount">
              <text :class="record.type === 'income' ? 'amount-income' : 'amount-expense'">
                {{ record.type === 'income' ? '+' : '-' }}¥{{ record.amount.toFixed(2) }}
              </text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 无数据提示 -->
    <view v-else class="empty-state card">
      <text class="large-text text-gray">
        {{ filterType === 'all' ? '还没有记录' : (filterType === 'income' ? '还没有收入记录' : '还没有支出记录') }}
      </text>
    </view>

    <!-- 记录详情弹窗 -->
    <view v-if="showDetailModal" class="modal-overlay" @click="closeDetail">
      <view class="detail-popup" @click.stop>
        <view class="popup-header">
          <text class="popup-title extra-large-text text-dark">记录详情</text>
          <button class="close-btn" @click="closeDetail">×</button>
        </view>
        <view v-if="selectedRecord" class="detail-content">
          <view class="detail-item">
            <text class="detail-label large-text text-gray">类型</text>
            <text class="detail-value large-text" :class="selectedRecord.type === 'income' ? 'primary-color' : 'danger-color'">
              {{ selectedRecord.type === 'income' ? '收入' : '支出' }}
            </text>
          </view>
          <view class="detail-item">
            <text class="detail-label large-text text-gray">金额</text>
            <text class="detail-value extra-large-text" :class="selectedRecord.type === 'income' ? 'amount-income' : 'amount-expense'">
              {{ selectedRecord.type === 'income' ? '+' : '-' }}¥{{ selectedRecord.amount.toFixed(2) }}
            </text>
          </view>
          <view class="detail-item">
            <text class="detail-label large-text text-gray">分类</text>
            <text class="detail-value large-text text-dark">{{ selectedRecord.category }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label large-text text-gray">日期</text>
            <text class="detail-value large-text text-dark">{{ formatDetailDate(selectedRecord.date) }}</text>
          </view>
          <view v-if="selectedRecord.note" class="detail-item">
            <text class="detail-label large-text text-gray">备注</text>
            <text class="detail-value large-text text-dark">{{ selectedRecord.note }}</text>
          </view>
        </view>
        <view class="popup-actions">
          <button class="action-btn edit-btn large-button" @click="editRecord">
            修改记录
          </button>
          <button class="action-btn delete-btn large-button" @click="deleteRecord">
            删除记录
          </button>
        </view>
      </view>
    </view>

    <!-- 自定义TabBar -->
    <custom-tab-bar></custom-tab-bar>
  </view>
</template>

<script>
import CustomTabBar from '../../custom-tab-bar/index.vue'

export default {
  components: {
    CustomTabBar
  },
  data() {
    return {
      records: [],
      filterType: 'all', // all, income, expense
      selectedRecord: null,
      showDetailModal: false
    }
  },
  
  computed: {
    filteredRecords() {
      if (this.filterType === 'all') {
        return this.records
      }
      return this.records.filter(record => record.type === this.filterType)
    },

    groupedRecords() {
      const groups = {}
      this.filteredRecords
        .sort((a, b) => new Date(b.date) - new Date(a.date))
        .forEach(record => {
          const date = record.date
          if (!groups[date]) {
            groups[date] = []
          }
          groups[date].push(record)
        })
      return groups
    },

    totalIncome() {
      return this.records
        .filter(record => record.type === 'income')
        .reduce((sum, record) => sum + record.amount, 0)
    },

    totalExpense() {
      return this.records
        .filter(record => record.type === 'expense')
        .reduce((sum, record) => sum + record.amount, 0)
    },

    totalBalance() {
      return this.totalIncome - this.totalExpense
    },

    incomeCount() {
      return this.records.filter(record => record.type === 'income').length
    },

    expenseCount() {
      return this.records.filter(record => record.type === 'expense').length
    },

    totalRecords() {
      return this.filteredRecords.length
    }
  },
  
  onShow() {
    this.loadData()
    // 通知TabBar更新选中状态
    this.$nextTick(() => {
      uni.$emit('tabBarUpdate')
    })
  },
  
  methods: {
    loadData() {
      this.records = uni.getStorageSync('accountRecords') || []
    },
    
    setFilter(type) {
      this.filterType = type
    },
    
    formatGroupDate(dateStr) {
      const date = new Date(dateStr)
      const today = new Date()
      const yesterday = new Date(today)
      yesterday.setDate(yesterday.getDate() - 1)
      
      if (date.toDateString() === today.toDateString()) {
        return '今天'
      } else if (date.toDateString() === yesterday.toDateString()) {
        return '昨天'
      } else {
        return `${date.getMonth() + 1}月${date.getDate()}日`
      }
    },
    
    formatDetailDate(dateStr) {
      const date = new Date(dateStr)
      return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`
    },
    
    getDayAmountClass(records) {
      let total = 0
      records.forEach(record => {
        if (record.type === 'income') {
          total += record.amount
        } else {
          total -= record.amount
        }
      })
      return total >= 0 ? 'amount-income' : 'amount-expense'
    },
    
    getDayAmountText(records) {
      let total = 0
      records.forEach(record => {
        if (record.type === 'income') {
          total += record.amount
        } else {
          total -= record.amount
        }
      })
      return `${total >= 0 ? '+' : ''}¥${total.toFixed(2)}`
    },
    
    showRecordDetail(record) {
      this.selectedRecord = record
      this.showDetailModal = true
    },

    showRecordOptions(record) {
      // 长按显示操作选项
      this.showRecordDetail(record)
    },

    closeDetail() {
      this.showDetailModal = false
      this.selectedRecord = null
    },

    editRecord() {
      if (!this.selectedRecord) return

      // 关闭详情弹窗
      this.closeDetail()

      // 跳转到记账页面，并传递记录信息用于编辑
      const record = this.selectedRecord
      const editData = {
        id: record.id,
        type: record.type,
        amount: record.amount.toString(),
        note: record.note,
        date: record.date,
        isEdit: true
      }

      // 将编辑数据存储到全局
      getApp().globalData = getApp().globalData || {}
      getApp().globalData.editRecord = editData

      // 跳转到记账页面
      uni.switchTab({
        url: '/pages/add/add'
      })
    },

    deleteRecord() {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除这条记录吗？',
        confirmText: '删除',
        confirmColor: '#f44336',
        success: (res) => {
          if (res.confirm) {
            // 从记录中删除
            const index = this.records.findIndex(r => r.id === this.selectedRecord.id)
            if (index > -1) {
              this.records.splice(index, 1)
              uni.setStorageSync('accountRecords', this.records)
              
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              })
              
              this.closeDetail()
            }
          }
        }
      })
    }
  }
}
</script>

<style scoped>
.container {
  background: linear-gradient(135deg, #fefefe 0%, #f8f8f8 25%, #f5f5f5 50%, #f2f2f2 75%, #efefef 100%);
  background-size: 400% 400%;
  animation: gradientShift 20s ease-in-out infinite;
  min-height: 100vh;
  padding-bottom: 140rpx; /* 为TabBar留出空间 */
  position: relative;
  overflow: hidden;
}

.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.9) 0%, transparent 35%),
    radial-gradient(circle at 80% 30%, rgba(248, 250, 252, 0.8) 0%, transparent 40%),
    radial-gradient(circle at 40% 80%, rgba(241, 245, 249, 0.7) 0%, transparent 45%),
    radial-gradient(circle at 70% 70%, rgba(255, 255, 255, 0.6) 0%, transparent 30%);
  pointer-events: none;
  animation: backgroundFloat 25s ease-in-out infinite;
}

.container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(4px 4px at 30px 40px, rgba(255, 255, 255, 0.7), transparent),
    radial-gradient(3px 3px at 50px 80px, rgba(255, 255, 255, 0.5), transparent),
    radial-gradient(2px 2px at 100px 50px, rgba(255, 255, 255, 0.8), transparent),
    radial-gradient(3px 3px at 140px 90px, rgba(255, 255, 255, 0.6), transparent),
    radial-gradient(2px 2px at 170px 40px, rgba(255, 255, 255, 0.7), transparent);
  background-repeat: repeat;
  background-size: 240px 140px;
  animation: snowfall 20s linear infinite;
  pointer-events: none;
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  25% { background-position: 100% 25%; }
  50% { background-position: 50% 100%; }
  75% { background-position: 25% 0%; }
}

@keyframes backgroundFloat {
  0%, 100% {
    transform: translateX(0) translateY(0) scale(1) rotate(0deg);
    opacity: 0.9;
  }
  25% {
    transform: translateX(15px) translateY(-8px) scale(1.03) rotate(1deg);
    opacity: 0.95;
  }
  50% {
    transform: translateX(-8px) translateY(12px) scale(0.97) rotate(-1deg);
    opacity: 0.85;
  }
  75% {
    transform: translateX(12px) translateY(-5px) scale(1.01) rotate(0.5deg);
    opacity: 0.92;
  }
}

@keyframes snowfall {
  0% {
    transform: translateY(-120vh) translateX(-30px) rotate(0deg);
  }
  100% {
    transform: translateY(120vh) translateX(150px) rotate(360deg);
  }
}

/* 顶部标题 */
.header {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(50rpx);
  -webkit-backdrop-filter: blur(50rpx);
  color: #1a202c;
  text-align: center;
  padding: 80rpx 40rpx 50rpx;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.4);
  position: relative;
  z-index: 2;
  box-shadow:
    0 12rpx 40rpx rgba(0, 0, 0, 0.03),
    0 4rpx 16rpx rgba(0, 0, 0, 0.02),
    inset 0 2rpx 0 rgba(255, 255, 255, 0.8);
}

.title {
  font-size: 56rpx;
  font-weight: 100;
  margin-bottom: 24rpx;
  color: #0f172a;
  letter-spacing: 6rpx;
  text-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  background: linear-gradient(135deg, #0f172a 0%, #374151 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  font-size: 32rpx;
  color: #374151;
  font-weight: 200;
  letter-spacing: 2rpx;
  opacity: 0.85;
  text-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.04);
}

/* 筛选选项 */
.filter-section {
  margin: 32rpx;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(30rpx);
  -webkit-backdrop-filter: blur(30rpx);
  border-radius: 32rpx;
  padding: 32rpx;
  position: relative;
  z-index: 2;
  box-shadow:
    0 16rpx 48rpx rgba(0, 0, 0, 0.06),
    0 4rpx 16rpx rgba(0, 0, 0, 0.04),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.7);
  border: 1rpx solid rgba(255, 255, 255, 0.4);
}

.filter-tabs {
  display: flex;
  background: rgba(241, 245, 249, 0.8);
  border-radius: 20rpx;
  padding: 6rpx;
}

.filter-tab {
  flex: 1;
  text-align: center;
  padding: 28rpx 20rpx;
  border-radius: 20rpx;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 3rpx solid transparent;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.tab-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #2D3748;
  letter-spacing: 2rpx;
}

.filter-tab.active .tab-text {
  color: white;
  font-weight: 800;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.filter-tab.active {
  background: rgba(255, 255, 255, 0.95);
  box-shadow:
    0 6rpx 20rpx rgba(0, 0, 0, 0.12),
    0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  transform: scale(1.05);
}

.filter-tab.active:not(.income):not(.expense) {
  border: 3rpx solid #3B82F6;
  box-shadow:
    0 6rpx 20rpx rgba(0, 0, 0, 0.12),
    0 0 0 2rpx rgba(59, 130, 246, 0.2);
}

.filter-tab.active:not(.income):not(.expense) .tab-text {
  color: #000000 !important;
  font-weight: 800;
}

.filter-tab.active.income {
  background: linear-gradient(135deg, #22C55E 0%, #16A34A 100%);
  border: 3rpx solid #15803D;
  box-shadow:
    0 6rpx 20rpx rgba(34, 197, 94, 0.4),
    0 0 0 2rpx rgba(34, 197, 94, 0.2);
}

.filter-tab.active.expense {
  background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
  border: 3rpx solid #B91C1C;
  box-shadow:
    0 6rpx 20rpx rgba(239, 68, 68, 0.4),
    0 0 0 2rpx rgba(239, 68, 68, 0.2);
}

/* 统计概览 */
.stats-overview {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  margin: 32rpx;
  position: relative;
  z-index: 2;
}

.stats-card {
  background: rgba(255, 255, 255, 0.12);
  backdrop-filter: blur(40rpx);
  -webkit-backdrop-filter: blur(40rpx);
  border-radius: 32rpx;
  padding: 40rpx 32rpx;
  display: flex;
  align-items: center;
  gap: 24rpx;
  box-shadow:
    0 16rpx 48rpx rgba(0, 0, 0, 0.06),
    0 4rpx 16rpx rgba(0, 0, 0, 0.04),
    inset 0 2rpx 0 rgba(255, 255, 255, 0.8),
    inset 0 -1rpx 0 rgba(255, 255, 255, 0.4);
  border: 1rpx solid rgba(255, 255, 255, 0.5);
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.2) 100%);
  pointer-events: none;
}

.stats-card:hover {
  transform: translateY(-4rpx) scale(1.02);
  box-shadow:
    0 24rpx 64rpx rgba(0, 0, 0, 0.08),
    0 8rpx 24rpx rgba(0, 0, 0, 0.06),
    inset 0 2rpx 0 rgba(255, 255, 255, 0.9),
    inset 0 -1rpx 0 rgba(255, 255, 255, 0.5);
}

.stats-icon {
  font-size: 48rpx;
  opacity: 0.9;
  position: relative;
  z-index: 2;
}

.stats-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  position: relative;
  z-index: 2;
}

.stats-label {
  font-size: 32rpx;
  color: #2D3748;
  font-weight: 600;
  letter-spacing: 2rpx;
}

.stats-amount {
  font-size: 52rpx;
  font-weight: 800;
  letter-spacing: 3rpx;
  text-shadow: 0 3rpx 6rpx rgba(0, 0, 0, 0.15);
}

.stats-amount.positive {
  color: #16A34A;
}

.stats-amount.negative {
  color: #DC2626;
}

.stats-detail {
  font-size: 24rpx;
  color: #718096;
  font-weight: 300;
  opacity: 0.8;
}

/* 记录列表 */
.records-section {
  margin: 32rpx;
  position: relative;
  z-index: 2;
}

.date-group {
  margin-bottom: 32rpx;
}

.date-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 32rpx 20rpx;
}

.date-text {
  font-size: 32rpx;
  font-weight: 300;
  color: #1a202c;
  letter-spacing: 1rpx;
}

.date-amount {
  font-size: 32rpx;
  font-weight: 300;
  letter-spacing: 1rpx;
}

.records-card {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(30rpx);
  -webkit-backdrop-filter: blur(30rpx);
  border-radius: 32rpx;
  overflow: hidden;
  box-shadow:
    0 16rpx 48rpx rgba(0, 0, 0, 0.06),
    0 4rpx 16rpx rgba(0, 0, 0, 0.04),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.7);
  border: 1rpx solid rgba(255, 255, 255, 0.4);
}

.record-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  cursor: pointer;
}

.record-item:last-child {
  border-bottom: none;
}

.record-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(8rpx);
}

.record-item:active {
  background: rgba(255, 255, 255, 0.15);
  transform: translateX(4rpx);
}

.record-main {
  flex: 1;
}

.record-category {
  font-size: 40rpx;
  font-weight: 700;
  color: #000000;
  margin-bottom: 16rpx;
  letter-spacing: 1rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.record-note {
  font-size: 32rpx;
  color: #2D3748;
  opacity: 0.9;
  font-weight: 500;
}

.record-amount {
  text-align: right;
  font-size: 44rpx;
  font-weight: 800;
  letter-spacing: 2rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.amount-income {
  color: #16A34A;
}

.amount-expense {
  color: #DC2626;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 48rpx;
  margin: 32rpx;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(30rpx);
  -webkit-backdrop-filter: blur(30rpx);
  border-radius: 32rpx;
  position: relative;
  z-index: 2;
  box-shadow:
    0 16rpx 48rpx rgba(0, 0, 0, 0.06),
    0 4rpx 16rpx rgba(0, 0, 0, 0.04),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.7);
  border: 1rpx solid rgba(255, 255, 255, 0.4);
}

.empty-state text {
  font-size: 36rpx;
  font-weight: 300;
  color: #4a5568;
  letter-spacing: 1rpx;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;
}

.detail-popup {
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  padding: 0;
  max-height: 65vh;
  width: 100%;
  animation: slideUp 0.3s ease-out;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.popup-title {
  font-weight: bold;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  border: none;
  background: #f5f5f5;
  color: #666;
  font-size: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.detail-content {
  padding: 20rpx 30rpx;
  flex: 1;
  overflow-y: auto;
  max-height: calc(65vh - 240rpx);
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  color: #666;
}

.detail-value {
  text-align: right;
  max-width: 60%;
  word-break: break-all;
}

.popup-actions {
  padding: 20rpx !important;
  border-top: 2rpx solid #e0e0e0;
  display: flex !important;
  gap: 16rpx;
  flex-shrink: 0 !important;
  background: white !important;
  height: 140rpx !important;
  width: 100% !important;
  align-items: center !important;
  justify-content: center !important;
  position: relative !important;
  z-index: 10 !important;
}

.edit-btn {
  flex: 1;
  height: 80rpx;
  background: linear-gradient(135deg, #22C55E 0%, #16A34A 100%) !important;
  color: #FFFFFF !important;
  border: 2rpx solid #16A34A !important;
  border-radius: 12rpx;
  font-size: 30rpx !important;
  font-weight: 700 !important;
  letter-spacing: 1rpx;
  text-shadow: none !important;
  box-shadow:
    0 4rpx 12rpx rgba(34, 197, 94, 0.4),
    0 2rpx 6rpx rgba(34, 197, 94, 0.3);
  transition: all 0.3s ease;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  text-align: center !important;
  line-height: 1.2 !important;
  position: relative !important;
  z-index: 11 !important;
}

.delete-btn {
  flex: 1;
  height: 80rpx;
  background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%) !important;
  color: #FFFFFF !important;
  border: 2rpx solid #DC2626 !important;
  border-radius: 12rpx;
  font-size: 30rpx !important;
  font-weight: 700 !important;
  letter-spacing: 1rpx;
  text-shadow: none !important;
  box-shadow:
    0 4rpx 12rpx rgba(239, 68, 68, 0.4),
    0 2rpx 6rpx rgba(239, 68, 68, 0.3);
  transition: all 0.3s ease;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  text-align: center !important;
  line-height: 1.2 !important;
  position: relative !important;
  z-index: 11 !important;
}

.edit-btn:active {
  transform: scale(0.98);
  box-shadow:
    0 4rpx 12rpx rgba(34, 197, 94, 0.3),
    0 2rpx 6rpx rgba(34, 197, 94, 0.2);
}

.delete-btn:active {
  transform: scale(0.98);
  box-shadow:
    0 4rpx 12rpx rgba(239, 68, 68, 0.3),
    0 2rpx 6rpx rgba(239, 68, 68, 0.2);
}
</style>
