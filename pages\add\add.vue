<template>
  <view class="container">
    <!-- 顶部装饰区域 -->
    <view class="header-decoration">
      <view class="floating-elements">
        <view class="float-circle circle-1"></view>
        <view class="float-circle circle-2"></view>
        <view class="float-circle circle-3"></view>
      </view>
      <view class="header-content">
        <text class="main-title">{{ pageTitle }}</text>
        <text class="main-subtitle">{{ pageSubtitle }}</text>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <view class="main-content">
      <!-- 类型选择卡片 -->
      <view class="type-card">
        <view class="card-header">
          <text class="card-title">记录类型</text>
          <view class="type-indicator" :class="currentType"></view>
        </view>
        <view class="type-switch">
          <view
            :class="['type-option', currentType === 'expense' ? 'active expense-active' : '']"
            @click="switchType('expense')"
          >
            <view class="type-icon expense-icon">💸</view>
            <text class="type-text">支出</text>
            <view class="type-desc">日常开销记录</view>
          </view>
          <view
            :class="['type-option', currentType === 'income' ? 'active income-active' : '']"
            @click="switchType('income')"
          >
            <view class="type-icon income-icon">💰</view>
            <text class="type-text">收入</text>
            <view class="type-desc">收入来源记录</view>
          </view>
        </view>
      </view>

      <!-- 金额输入卡片 -->
      <view class="amount-card">
        <view class="card-header">
          <text class="card-title">{{ amountLabel }}</text>
          <view class="amount-status" :class="amount ? 'filled' : 'empty'">
            {{ amount ? '✓' : '○' }}
          </view>
        </view>
        <view class="amount-input-container">
          <view class="currency-wrapper">
            <text class="currency-symbol">¥</text>
          </view>
          <input
            class="amount-input"
            type="digit"
            placeholder="0.00"
            v-model="amount"
            @input="onAmountInput"
            @focus="onAmountFocus"
            @blur="onAmountBlur"
          />
          <view class="amount-decoration"></view>
        </view>
        <view class="amount-hint">
          <text class="hint-text">{{ currentType === 'income' ? '记录您的收入金额' : '记录您的支出金额' }}</text>
        </view>
      </view>


      <!-- 备注输入卡片 -->
      <view class="note-card">
        <view class="card-header">
          <text class="card-title">备注说明</text>
          <view class="required-badge">必填</view>
        </view>
        <view class="note-input-container">
          <textarea
            class="note-input"
            placeholder="请详细说明这笔收支的用途，比如：买菜、看病、工资等"
            v-model="note"
            maxlength="100"
            @focus="onNoteFocus"
            @blur="onNoteBlur"
          />
          <view class="note-counter">{{ note.length }}/100</view>
        </view>
        <view class="note-suggestions">
          <text class="suggestion-title">常用备注：</text>
          <view class="suggestion-tags">
            <view
              v-for="tag in getSuggestionTags()"
              :key="tag"
              class="suggestion-tag"
              @click="selectSuggestion(tag)"
            >
              {{ tag }}
            </view>
          </view>
        </view>
      </view>

      <!-- 日期选择卡片 -->
      <view class="date-card">
        <view class="card-header">
          <text class="card-title">选择日期</text>
          <view class="date-status">📅</view>
        </view>
        <picker
          mode="date"
          :value="selectedDate"
          @change="onDateChange"
          class="date-picker"
        >
          <view class="date-display">
            <view class="date-main">{{ formatDisplayDate(selectedDate) }}</view>
            <view class="date-detail">{{ formatDetailDate(selectedDate) }}</view>
          </view>
        </picker>
      </view>

      <!-- 保存按钮 -->
      <view class="save-section">
        <button
          :class="['save-btn', currentType, !canSave ? 'disabled' : '']"
          @click="saveRecord"
          :disabled="!canSave"
        >
          {{ saveButtonText }}
        </button>
      </view>
    </view>

    <!-- 自定义TabBar -->
    <custom-tab-bar></custom-tab-bar>
  </view>
</template>

<script>
import CustomTabBar from '../../custom-tab-bar/index.vue'

export default {
  components: {
    CustomTabBar
  },
  data() {
    return {
      currentType: 'expense', // 默认支出
      amount: '',
      note: '',
      selectedDate: '',
      isEditMode: false, // 是否为编辑模式
      editRecordId: null // 编辑的记录ID
    }
  },

  computed: {
    canSave() {
      return this.amount && parseFloat(this.amount) > 0 && this.note.trim()
    },

    pageTitle() {
      if (this.isEditMode) {
        return this.currentType === 'income' ? '修改收入记录' : '修改支出记录'
      }
      return this.currentType === 'income' ? '记录收入' : '记录支出'
    },

    pageSubtitle() {
      if (this.isEditMode) {
        return '修改已有的收支记录信息'
      }
      return this.currentType === 'income' ? '记录家庭收入情况' : '记录日常开销情况'
    },

    amountLabel() {
      return this.currentType === 'income' ? '收入金额' : '支出金额'
    },

    saveButtonText() {
      return this.isEditMode ? '更新收支记录' : '保存收支记录'
    }
  },

  onLoad(options) {
    // 从参数获取类型
    if (options && options.type) {
      this.currentType = options.type
    }

    // 设置默认日期为今天
    const today = new Date()
    this.selectedDate = today.toISOString().split('T')[0]
  },

  onShow() {
    // 检查全局数据中是否有类型参数
    const app = getApp()
    if (app.globalData && app.globalData.addType) {
      this.currentType = app.globalData.addType
      // 清除全局数据，避免重复使用
      delete app.globalData.addType
    }

    // 检查是否有编辑数据
    if (app.globalData && app.globalData.editRecord) {
      const editData = app.globalData.editRecord
      this.isEditMode = true
      this.editRecordId = editData.id
      this.currentType = editData.type
      this.amount = editData.amount
      this.note = editData.note
      this.selectedDate = editData.date

      // 清除全局数据
      delete app.globalData.editRecord
    }

    // 通知TabBar更新选中状态
    this.$nextTick(() => {
      uni.$emit('tabBarUpdate')
    })
  },

  methods: {
    switchType(type) {
      this.currentType = type
    },

    onAmountFocus() {
      // 金额输入框获得焦点时的动画效果
    },

    onAmountBlur() {
      // 金额输入框失去焦点时的动画效果
    },

    onNoteFocus() {
      // 备注输入框获得焦点时的动画效果
    },

    onNoteBlur() {
      // 备注输入框失去焦点时的动画效果
    },

    getSuggestionTags() {
      if (this.currentType === 'income') {
        return ['工资', '奖金', '兼职', '投资', '礼金', '其他收入']
      } else {
        return ['餐饮', '交通', '购物', '医疗', '娱乐', '生活用品']
      }
    },

    selectSuggestion(tag) {
      this.note = tag
    },

    formatDetailDate(dateStr) {
      const date = new Date(dateStr)
      const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
      return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日 ${weekdays[date.getDay()]}`
    },

    onAmountInput(e) {
      let value = e.detail.value

      // 只允许数字和小数点
      value = value.replace(/[^\d.]/g, '')

      // 限制只能有一个小数点
      const dotCount = (value.match(/\./g) || []).length
      if (dotCount > 1) {
        value = value.substring(0, value.lastIndexOf('.'))
      }

      // 限制小数点后两位
      if (value.includes('.')) {
        const parts = value.split('.')
        if (parts[1] && parts[1].length > 2) {
          value = parts[0] + '.' + parts[1].substring(0, 2)
        }
      }

      // 限制最大金额
      const numValue = parseFloat(value)
      if (numValue > 999999.99) {
        uni.showToast({
          title: '金额不能超过99万',
          icon: 'none'
        })
        return
      }

      this.amount = value
    },

    onDateChange(e) {
      this.selectedDate = e.detail.value
    },

    formatDisplayDate(dateStr) {
      const date = new Date(dateStr)
      const today = new Date()
      const yesterday = new Date(today)
      yesterday.setDate(yesterday.getDate() - 1)

      if (date.toDateString() === today.toDateString()) {
        return '今天'
      } else if (date.toDateString() === yesterday.toDateString()) {
        return '昨天'
      } else {
        return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`
      }
    },
    saveRecord() {
      if (!this.canSave) {
        let message = '请填写完整信息'
        if (!this.amount || parseFloat(this.amount) <= 0) {
          message = '请输入有效金额'
        } else if (!this.note.trim()) {
          message = '请填写备注说明'
        }

        uni.showToast({
          title: message,
          icon: 'none',
          duration: 2000
        })
        return
      }

      // 确认保存
      const typeText = this.currentType === 'income' ? '收入' : '支出'
      const amountText = `¥${parseFloat(this.amount).toFixed(0)}`
      const confirmText = `确认保存这笔${typeText}记录吗？\n\n金额：${amountText}\n备注：${this.note.trim()}`

      uni.showModal({
        title: '确认保存',
        content: confirmText,
        confirmText: '保存',
        confirmColor: this.currentType === 'income' ? '#10b981' : '#f59e0b',
        success: (res) => {
          if (res.confirm) {
            this.doSaveRecord()
          }
        }
      })
    },

    doSaveRecord() {
      const records = uni.getStorageSync('accountRecords') || []

      if (this.isEditMode) {
        // 编辑模式：更新现有记录
        const index = records.findIndex(r => r.id === this.editRecordId)
        if (index > -1) {
          // 保留原有的id和createTime，更新其他字段
          records[index] = {
            ...records[index],
            type: this.currentType,
            amount: parseFloat(this.amount),
            category: this.note.trim(),
            note: this.note.trim(),
            date: this.selectedDate,
            updateTime: new Date().toISOString()
          }

          uni.setStorageSync('accountRecords', records)

          const typeText = this.currentType === 'income' ? '收入' : '支出'
          uni.showToast({
            title: `${typeText}记录更新成功！`,
            icon: 'success',
            duration: 1500
          })
        }
      } else {
        // 新增模式：创建新记录
        const record = {
          id: Date.now(),
          type: this.currentType,
          amount: parseFloat(this.amount),
          category: this.note.trim(),
          note: this.note.trim(),
          date: this.selectedDate,
          createTime: new Date().toISOString()
        }

        records.push(record)
        uni.setStorageSync('accountRecords', records)

        const typeText = this.currentType === 'income' ? '收入' : '支出'
        uni.showToast({
          title: `${typeText}记录保存成功！`,
          icon: 'success',
          duration: 1500
        })
      }

      // 清空表单和重置状态
      this.amount = ''
      this.note = ''
      this.isEditMode = false
      this.editRecordId = null

      // 延迟返回首页
      setTimeout(() => {
        uni.switchTab({
          url: '/pages/index/index'
        })
      }, 1000)
    }
  }
}
</script>

<style scoped>
.container {
  background: linear-gradient(135deg, #fefefe 0%, #f8f8f8 25%, #f5f5f5 50%, #f2f2f2 75%, #efefef 100%);
  background-size: 400% 400%;
  animation: gradientShift 20s ease-in-out infinite;
  min-height: 100vh;
  padding-bottom: 180rpx; /* 为TabBar留出空间 */
  position: relative;
  overflow: hidden;
}

.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 15% 15%, rgba(255, 255, 255, 0.8) 0%, transparent 40%),
    radial-gradient(circle at 85% 25%, rgba(240, 242, 247, 0.6) 0%, transparent 45%),
    radial-gradient(circle at 45% 85%, rgba(248, 250, 252, 0.7) 0%, transparent 50%);
  pointer-events: none;
  animation: backgroundFloat 20s ease-in-out infinite;
}

.container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(8px 8px at 30px 40px, rgba(255, 255, 255, 0.8), transparent),
    radial-gradient(6px 6px at 60px 80px, rgba(255, 255, 255, 0.6), transparent),
    radial-gradient(4px 4px at 100px 50px, rgba(255, 255, 255, 0.9), transparent),
    radial-gradient(7px 7px at 140px 90px, rgba(255, 255, 255, 0.7), transparent),
    radial-gradient(5px 5px at 180px 40px, rgba(255, 255, 255, 0.8), transparent),
    radial-gradient(6px 6px at 200px 70px, rgba(255, 255, 255, 0.5), transparent),
    radial-gradient(3px 3px at 250px 30px, rgba(255, 255, 255, 0.9), transparent),
    radial-gradient(9px 9px at 280px 100px, rgba(255, 255, 255, 0.6), transparent);
  background-repeat: repeat;
  background-size: 320px 160px;
  animation: snowfall 25s linear infinite;
  pointer-events: none;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  25% { background-position: 100% 25%; }
  50% { background-position: 50% 100%; }
  75% { background-position: 25% 0%; }
  100% { background-position: 0% 50%; }
}

@keyframes backgroundFloat {
  0%, 100% {
    transform: translateX(0) translateY(0) scale(1) rotate(0deg);
    opacity: 0.9;
  }
  25% {
    transform: translateX(15px) translateY(-8px) scale(1.05) rotate(2deg);
    opacity: 0.95;
  }
  50% {
    transform: translateX(-8px) translateY(12px) scale(0.95) rotate(-1deg);
    opacity: 0.85;
  }
  75% {
    transform: translateX(12px) translateY(-5px) scale(1.02) rotate(1deg);
    opacity: 0.92;
  }
}

@keyframes snowfall {
  0% {
    transform: translateY(-120vh) translateX(-40px) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(120vh) translateX(160px) rotate(180deg);
    opacity: 0;
  }
}

/* 顶部装饰区域 */
.header-decoration {
  position: relative;
  height: 280rpx;
  overflow: hidden;
}

.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.float-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
}

.circle-1 {
  width: 120rpx;
  height: 120rpx;
  top: 40rpx;
  left: 60rpx;
  animation: float1 8s ease-in-out infinite;
}

.circle-2 {
  width: 80rpx;
  height: 80rpx;
  top: 80rpx;
  right: 80rpx;
  animation: float2 6s ease-in-out infinite reverse;
}

.circle-3 {
  width: 60rpx;
  height: 60rpx;
  top: 160rpx;
  left: 50%;
  transform: translateX(-50%);
  animation: float3 10s ease-in-out infinite;
}

@keyframes float1 {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-20rpx) rotate(180deg); }
}

@keyframes float2 {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-15rpx) rotate(-180deg); }
}

@keyframes float3 {
  0%, 100% { transform: translateX(-50%) translateY(0) rotate(0deg); }
  50% { transform: translateX(-50%) translateY(-25rpx) rotate(360deg); }
}

.header-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 2;
}

.main-title {
  font-size: 72rpx;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 24rpx;
  letter-spacing: 6rpx;
  text-shadow: 0 3rpx 12rpx rgba(0, 0, 0, 0.15);
  display: block;
}

.main-subtitle {
  font-size: 40rpx;
  color: #2d3748;
  font-weight: 400;
  letter-spacing: 3rpx;
  text-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

/* 主要内容区域 */
.main-content {
  padding: 32rpx;
  position: relative;
  z-index: 2;
}

/* 通用卡片样式 */
.type-card, .amount-card, .note-card, .date-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(40rpx);
  -webkit-backdrop-filter: blur(40rpx);
  border-radius: 32rpx;
  margin-bottom: 32rpx;
  padding: 40rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 20rpx 60rpx rgba(0, 0, 0, 0.1),
    0 8rpx 24rpx rgba(0, 0, 0, 0.08),
    inset 0 2rpx 0 rgba(255, 255, 255, 0.3);
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

.type-card:hover, .amount-card:hover, .note-card:hover, .date-card:hover {
  transform: translateY(-8rpx) scale(1.02);
  box-shadow:
    0 32rpx 80rpx rgba(0, 0, 0, 0.15),
    0 12rpx 32rpx rgba(0, 0, 0, 0.12),
    inset 0 2rpx 0 rgba(255, 255, 255, 0.4);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.card-title {
  font-size: 48rpx;
  font-weight: 600;
  color: #1a202c;
  letter-spacing: 3rpx;
}

/* 类型选择卡片 */
.type-indicator {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.type-indicator.income {
  background: linear-gradient(135deg, #10b981, #059669);
  box-shadow: 0 4rpx 12rpx rgba(16, 185, 129, 0.4);
}

.type-indicator.expense {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  box-shadow: 0 4rpx 12rpx rgba(245, 158, 11, 0.4);
}

.type-switch {
  display: flex;
  gap: 24rpx;
}

.type-option {
  flex: 1;
  text-align: center;
  padding: 32rpx 24rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.1);
  border: 3rpx solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  cursor: pointer;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.type-option.active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.08);
  box-shadow:
    0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

/* 支出选中状态 - 红色边框 */
.type-option.active.expense-active {
  border: 6rpx solid #EF4444;
  box-shadow:
    0 8rpx 24rpx rgba(0, 0, 0, 0.15),
    0 0 0 4rpx rgba(239, 68, 68, 0.2);
}

/* 收入选中状态 - 绿色边框 */
.type-option.active.income-active {
  border: 6rpx solid #22C55E;
  box-shadow:
    0 8rpx 24rpx rgba(0, 0, 0, 0.15),
    0 0 0 4rpx rgba(34, 197, 94, 0.2);
}

.type-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
  display: block;
}

.type-text {
  font-size: 44rpx;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 12rpx;
  display: block;
}

.type-desc {
  font-size: 32rpx;
  color: #2d3748;
  font-weight: 400;
}

/* 金额输入卡片 */
.amount-status {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
  transition: all 0.3s ease;
}

.amount-status.filled {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(16, 185, 129, 0.4);
}

.amount-status.empty {
  background: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.6);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.amount-input-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24rpx;
}

.currency-wrapper {
  margin-right: 16rpx;
}

.currency-symbol {
  font-size: 96rpx;
  font-weight: 600;
  color: #2d3748;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.amount-input {
  border: none;
  outline: none;
  background: transparent;
  text-align: center;
  font-size: 96rpx;
  font-weight: 700;
  color: #1a202c;
  min-width: 400rpx;
  letter-spacing: 6rpx;
  text-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.15);
}

.amount-input::placeholder {
  color: #718096;
  font-weight: 400;
}

.amount-decoration {
  position: absolute;
  bottom: -8rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 80rpx;
  height: 4rpx;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
  border-radius: 2rpx;
}

.amount-hint {
  text-align: center;
}

.hint-text {
  font-size: 26rpx;
  color: #4a5568;
  font-weight: 200;
  letter-spacing: 1rpx;
}

/* 备注输入卡片 */
.required-badge {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  font-size: 20rpx;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-weight: 400;
  box-shadow: 0 2rpx 8rpx rgba(245, 158, 11, 0.3);
}

.note-input-container {
  position: relative;
  margin-bottom: 24rpx;
}

.note-input {
  width: 100%;
  min-height: 200rpx;
  padding: 32rpx;
  border: 3rpx solid rgba(0, 0, 0, 0.15);
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  resize: none;
  font-size: 40rpx;
  font-weight: 400;
  color: #1a202c;
  line-height: 1.8;
  transition: all 0.3s ease;
}

.note-input::placeholder {
  color: #a0aec0;
}

.note-input:focus {
  border-color: rgba(0, 0, 0, 0.2);
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 0 0 4rpx rgba(0, 0, 0, 0.05);
}

.note-counter {
  position: absolute;
  bottom: 12rpx;
  right: 16rpx;
  font-size: 22rpx;
  color: #a0aec0;
  font-weight: 200;
}

.note-suggestions {
  margin-top: 16rpx;
}

.suggestion-title {
  font-size: 26rpx;
  color: #4a5568;
  margin-bottom: 16rpx;
  display: block;
}

.suggestion-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.suggestion-tag {
  padding: 12rpx 20rpx;
  background: rgba(255, 255, 255, 0.8);
  border: 1rpx solid rgba(0, 0, 0, 0.1);
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #2d3748;
  transition: all 0.3s ease;
  cursor: pointer;
}

.suggestion-tag:hover {
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(0, 0, 0, 0.2);
  transform: translateY(-2rpx);
}

/* 日期选择卡片 */
.date-status {
  font-size: 32rpx;
  opacity: 0.8;
}

.date-picker {
  width: 100%;
}

.date-display {
  background: rgba(255, 255, 255, 0.8);
  border: 2rpx solid rgba(0, 0, 0, 0.1);
  border-radius: 20rpx;
  padding: 32rpx 24rpx;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.date-display:hover {
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(0, 0, 0, 0.2);
  transform: translateY(-2rpx);
}

.date-main {
  font-size: 36rpx;
  font-weight: 400;
  color: #2d3748;
  margin-bottom: 8rpx;
}

.date-detail {
  font-size: 24rpx;
  color: #4a5568;
  font-weight: 200;
}

/* 保存按钮 */
.save-section {
  padding: 32rpx;
  padding-bottom: 60rpx;
}

.save-btn {
  width: 100% !important;
  height: 200rpx !important;
  border-radius: 40rpx !important;
  font-size: 72rpx !important;
  font-weight: 900 !important;
  font-weight: 900 !important;
  letter-spacing: 8rpx !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  overflow: hidden !important;
  cursor: pointer !important;

  /* 明显的按钮背景 */
  background: linear-gradient(135deg,
    #16A34A 0%,
    #15803D 50%,
    #166534 100%) !important;

  /* 白色文字 */
  color: #FFFFFF !important;

  /* 强烈的边框 */
  border: 6rpx solid #15803D !important;

  /* 立体阴影效果 */
  box-shadow:
    0 20rpx 50rpx rgba(22, 163, 74, 0.4),
    0 10rpx 25rpx rgba(22, 163, 74, 0.3),
    0 5rpx 12rpx rgba(0, 0, 0, 0.2),
    inset 0 4rpx 0 rgba(255, 255, 255, 0.3),
    inset 0 -3rpx 0 rgba(0, 0, 0, 0.2) !important;

  /* 文字阴影 */
  text-shadow:
    0 4rpx 8rpx rgba(0, 0, 0, 0.3),
    0 2rpx 4rpx rgba(0, 0, 0, 0.5) !important;
}

/* 收入和支出按钮样式已统一到主样式中 */

.save-btn:not(.disabled):hover {
  transform: translateY(-8rpx) scale(1.05);
  background: linear-gradient(135deg,
    #15803D 0%,
    #166534 50%,
    #14532D 100%) !important;
  box-shadow:
    0 25rpx 60rpx rgba(22, 163, 74, 0.5),
    0 15rpx 30rpx rgba(22, 163, 74, 0.4),
    0 8rpx 15rpx rgba(0, 0, 0, 0.3),
    inset 0 4rpx 0 rgba(255, 255, 255, 0.4),
    inset 0 -3rpx 0 rgba(0, 0, 0, 0.3) !important;
}

.save-btn:not(.disabled):active {
  transform: translateY(-4rpx) scale(1.02);
  background: linear-gradient(135deg,
    #166534 0%,
    #14532D 50%,
    #15803D 100%) !important;
  box-shadow:
    0 15rpx 40rpx rgba(22, 163, 74, 0.4),
    0 8rpx 20rpx rgba(22, 163, 74, 0.3),
    inset 0 2rpx 0 rgba(255, 255, 255, 0.2),
    inset 0 -2rpx 0 rgba(0, 0, 0, 0.4) !important;
}

.save-btn.disabled {
  background: linear-gradient(135deg,
    #E5E7EB 0%,
    #D1D5DB 50%,
    #9CA3AF 100%) !important;
  color: #22C55E !important;
  border: 6rpx solid #D1D5DB !important;
  box-shadow:
    0 8rpx 20rpx rgba(0, 0, 0, 0.1),
    inset 0 2rpx 0 rgba(255, 255, 255, 0.5),
    inset 0 -2rpx 0 rgba(0, 0, 0, 0.1) !important;
  cursor: not-allowed !important;
  transform: none !important;
  font-size: 72rpx !important;
  font-weight: 900 !important;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2) !important;
}

/* 启用状态样式已统一到主样式中 */

/* 强制确保禁用状态显示绿色字体 */
.save-btn.disabled,
.save-btn.disabled:hover,
.save-btn.disabled:active,
.save-btn.disabled.income,
.save-btn.disabled.expense {
  color: #22C55E !important;
  background: linear-gradient(135deg,
    #E5E7EB 0%,
    #D1D5DB 50%,
    #9CA3AF 100%) !important;
  border: 6rpx solid #D1D5DB !important;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2) !important;
  transform: none !important;
  cursor: not-allowed !important;
  box-shadow:
    0 8rpx 20rpx rgba(0, 0, 0, 0.1),
    inset 0 2rpx 0 rgba(255, 255, 255, 0.5),
    inset 0 -2rpx 0 rgba(0, 0, 0, 0.1) !important;
}




</style>
