{"name": "simple-account-book", "version": "1.0.0", "description": "简单家庭记账本", "main": "main.js", "scripts": {"serve": "npm run dev:h5", "build": "npm run build:h5", "dev:h5": "cross-env NODE_ENV=development UNI_PLATFORM=h5 vue-cli-service uni-serve", "build:h5": "cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build", "dev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch", "build:mp-weixin": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build"}, "dependencies": {"vue": "^2.6.14"}, "devDependencies": {"cross-env": "^7.0.3"}, "browserslist": ["Android >= 4.4", "ios >= 9"]}