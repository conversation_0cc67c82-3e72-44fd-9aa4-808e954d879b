<template>
  <view class="custom-tab-bar">
    <view
      v-for="(item, index) in tabList"
      :key="index"
      class="tab-item"
      :class="{ 'active': selected === index }"
      @click="switchTab(index)"
    >
      <!-- 图标 -->
      <view class="tab-icon">
        <text class="icon-text">{{ item.icon }}</text>
      </view>
      <!-- 文字 -->
      <view class="tab-text">{{ item.text }}</view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      selected: 0,
      tabList: [
        {
          pagePath: "pages/index/index",
          text: "首页",
          icon: "🏠" // 首页图标
        },
        {
          pagePath: "pages/add/add",
          text: "记账",
          icon: "💰" // 记账图标
        },
        {
          pagePath: "pages/statistics/statistics",
          text: "统计",
          icon: "📊" // 统计图标
        },
        {
          pagePath: "pages/history/history",
          text: "记录",
          icon: "📋" // 记录图标
        }
      ]
    }
  },
  
  created() {
    this.updateSelected()
    // 监听页面切换事件
    uni.$on('tabBarUpdate', () => {
      this.$nextTick(() => {
        this.updateSelected()
      })
    })
  },

  mounted() {
    this.updateSelected()
  },

  beforeDestroy() {
    // 移除事件监听
    uni.$off('tabBarUpdate')
  },
  
  methods: {
    switchTab(index) {
      if (this.selected === index) return

      this.selected = index
      const item = this.tabList[index]

      uni.switchTab({
        url: '/' + item.pagePath
      })
    },

    updateSelected() {
      // 获取当前页面路径，设置选中状态
      const pages = getCurrentPages()
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1]
        const currentRoute = currentPage.route

        this.tabList.forEach((item, index) => {
          if (item.pagePath === currentRoute) {
            this.selected = index
          }
        })
      }
    }
  },

  // 监听页面显示，更新选中状态
  onShow() {
    this.updateSelected()
  }
}
</script>

<style>

.custom-tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 140rpx;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20rpx);
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 16rpx 0;
  z-index: 9999;
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 12rpx 8rpx;
  border-radius: 24rpx;
  transition: all 0.15s ease-out;
  position: relative;
  margin: 0 8rpx;
}

/* 默认状态 */
.tab-icon {
  margin-bottom: 8rpx;
  transition: all 0.15s ease-out;
  display: flex;
  justify-content: center;
  align-items: center;
}

.icon-text {
  font-size: 48rpx;
  transition: all 0.15s ease-out;
}

.tab-text {
  font-size: 22rpx;
  color: #4A5568;
  font-weight: 600;
  transition: all 0.15s ease-out;
  line-height: 1.2;
}

/* 选中状态 - 淡雅优化 */
.tab-item.active {
  background: linear-gradient(135deg,
    rgba(22, 163, 74, 0.12) 0%,
    rgba(22, 163, 74, 0.08) 50%,
    rgba(22, 163, 74, 0.06) 100%);
  border: 2rpx solid rgba(22, 163, 74, 0.5);
  transform: translateY(-2rpx) scale(1.03);
  box-shadow:
    0 6rpx 16rpx rgba(22, 163, 74, 0.15),
    0 2rpx 8rpx rgba(22, 163, 74, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.7),
    0 0 12rpx rgba(22, 163, 74, 0.1);
}

.tab-item.active .tab-icon {
  transform: scale(1.08);
  filter:
    drop-shadow(0 2rpx 4rpx rgba(22, 163, 74, 0.3))
    drop-shadow(0 1rpx 2rpx rgba(22, 163, 74, 0.4));
}

.tab-item.active .icon-text {
  font-size: 50rpx;
  transform: scale(1.05);
}

.tab-item.active .tab-text {
  color: #16A34A;
  font-size: 23rpx;
  font-weight: 700;
  transform: scale(1.05);
  text-shadow:
    0 1rpx 2rpx rgba(22, 163, 74, 0.3),
    0 0 4rpx rgba(22, 163, 74, 0.2);
}

/* 点击效果 - 轻微优化 */
.tab-item:active {
  transform: translateY(-1rpx) scale(1.02);
}

.tab-item.active:active {
  transform: translateY(-1rpx) scale(1.01);
}
</style>
