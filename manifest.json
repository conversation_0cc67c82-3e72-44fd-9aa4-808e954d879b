{"name": "温馨记账本", "appid": "__UNI__WARM_ACCOUNT", "description": "专为家庭设计的温馨记账应用", "versionName": "1.0.0", "versionCode": "100", "transformPx": false, "locale": "auto", "app-plus": {"usingComponents": true, "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}, "modules": {}, "distribute": {"android": {"permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\" />", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\" />", "<uses-permission android:name=\"android.permission.VIBRATE\" />", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\" />", "<uses-permission android:name=\"android.permission.INTERNET\" />", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\" />", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\" />", "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\" />", "<uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\" />"]}, "ios": {}, "sdkConfigs": {}}}, "quickapp": {}, "mp-weixin": {"appid": "", "setting": {"urlCheck": false}, "usingComponents": true}, "mp-alipay": {"usingComponents": true}, "mp-baidu": {"usingComponents": true}, "mp-toutiao": {"usingComponents": true}, "uniStatistics": {"enable": false}, "vueVersion": "2"}