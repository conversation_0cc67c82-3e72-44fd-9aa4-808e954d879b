<script>
export default {
  onLaunch: function() {
    console.log('家庭记账本启动')
  },
  onShow: function() {
    console.log('App Show')
  },
  onHide: function() {
    console.log('App Hide')
  }
}
</script>

<style>
/* 全局样式 - 老年人友好设计 */
page {
  background: #f5f5f5;
  font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', Arial, sans-serif;
  font-size: 36rpx; /* 基础字体加大 */
  line-height: 1.6; /* 增加行高 */
}

/* 通用样式 */
.container {
  max-width: 750rpx;
  margin: 0 auto;
  background: white;
  min-height: 100vh;
}

/* 按钮样式 - 老年人友好 */
button {
  border: none;
  border-radius: 16rpx;
  font-weight: 600;
  font-size: 36rpx !important;
  min-height: 88rpx;
  padding: 16rpx 32rpx;
  transition: all 0.3s;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

button:active {
  transform: scale(0.96);
}

button:hover {
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.15);
}

/* 金额样式 */
.amount-income { color: #4facfe; }
.amount-expense { color: #fa709a; }

/* TabBar 老年人友好设计 */
.uni-tabbar {
  height: 120px !important;
  padding-top: 12px !important;
  padding-bottom: 0px !important;
  border-top: 3px solid #e2e8f0 !important;
  box-shadow: 0 -8px 32px rgba(0, 0, 0, 0.15) !important;
  backdrop-filter: blur(30px) !important;
  -webkit-backdrop-filter: blur(30px) !important;
  background: rgba(255, 255, 255, 0.95) !important;
}

/* 覆盖uni-app默认的bottom定位 */
.uni-tabbar.uni-tabbar-bottom .uni-tabbar {
  bottom: auto !important;
}

.uni-tabbar-item {
  height: 120px !important;
  padding: 8px 4px !important;
  min-width: 80px !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  border-radius: 16px !important;
  margin: 4px 2px !important;
}

.uni-tabbar .uni-tabbar-item .uni-tabbar-item-text {
  font-size: 18px !important;
  line-height: 1.2 !important;
  margin-top: 2px !important;
  font-weight: 800 !important;
  color: #2D3748 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.15) !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  white-space: pre-line !important;
  text-align: center !important;
}

/* 让emoji图标在上方且超大 */
.uni-tabbar .uni-tabbar-item .uni-tabbar-item-text::first-line {
  font-size: 48px !important;
  line-height: 1.0 !important;
  filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.2)) !important;
  display: block !important;
  margin-bottom: 4px !important;
}

/* 超美观的选中状态 */
.uni-tabbar .uni-tabbar-item.uni-tabbar-item-active {
  background: linear-gradient(135deg,
    rgba(22, 163, 74, 0.35) 0%,
    rgba(22, 163, 74, 0.25) 50%,
    rgba(22, 163, 74, 0.20) 100%) !important;
  border: 5px solid #16A34A !important;
  border-radius: 32px !important;
  transform: translateY(-8px) scale(1.15) !important;
  box-shadow:
    0 20px 55px rgba(22, 163, 74, 0.5) !important,
    0 12px 25px rgba(22, 163, 74, 0.35) !important,
    0 6px 12px rgba(22, 163, 74, 0.25) !important,
    0 0 40px rgba(22, 163, 74, 0.4) !important;
}

.uni-tabbar .uni-tabbar-item.uni-tabbar-item-active .uni-tabbar-item-text {
  color: #15803D !important;
  font-weight: 900 !important;
  font-size: 22px !important;
  transform: scale(1.2) !important;
  text-shadow:
    0 5px 10px rgba(22, 163, 74, 0.6) !important,
    0 3px 6px rgba(22, 163, 74, 0.8) !important,
    0 1px 3px rgba(22, 163, 74, 0.9) !important,
    0 0 15px rgba(22, 163, 74, 0.5) !important,
    0 0 25px rgba(22, 163, 74, 0.3) !important;
}

/* 选中时图标发光效果 */
.uni-tabbar .uni-tabbar-item.uni-tabbar-item-active .uni-tabbar-item-text::first-line {
  filter:
    drop-shadow(0 6px 12px rgba(22, 163, 74, 0.7)) !important,
    drop-shadow(0 3px 8px rgba(22, 163, 74, 0.6)) !important,
    drop-shadow(0 0 20px rgba(22, 163, 74, 0.5)) !important,
    drop-shadow(0 0 35px rgba(22, 163, 74, 0.3)) !important;
  transform: scale(1.25) !important;
  color: #15803D !important;
}

/* 老年人友好的通用样式 */
.elderly-friendly {
  font-size: 40rpx !important;
  line-height: 1.8 !important;
  font-weight: 500 !important;
}

/* 高对比度文字 */
.high-contrast-text {
  color: #1A202C !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

/* 大按钮样式 */
.large-button {
  min-height: 120rpx !important;
  font-size: 42rpx !important;
  font-weight: 600 !important;
  border-radius: 24rpx !important;
  padding: 24rpx 48rpx !important;
}

/* 输入框老年人友好样式 */
input, textarea {
  font-size: 40rpx !important;
  line-height: 1.8 !important;
  padding: 24rpx !important;
  border: 3rpx solid #E2E8F0 !important;
  border-radius: 16rpx !important;
}

input:focus, textarea:focus {
  border-color: #3182CE !important;
  box-shadow: 0 0 0 4rpx rgba(49, 130, 206, 0.1) !important;
}
</style>
