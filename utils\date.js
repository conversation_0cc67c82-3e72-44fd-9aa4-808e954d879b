/**
 * 日期工具类
 * 提供各种日期格式化和计算功能
 */

/**
 * 格式化日期为显示文本
 * @param {String|Date} date 日期
 * @param {String} format 格式类型: 'relative' | 'full' | 'short'
 */
export function formatDate(date, format = 'relative') {
  const targetDate = new Date(date)
  const today = new Date()
  const yesterday = new Date(today)
  yesterday.setDate(yesterday.getDate() - 1)
  
  switch (format) {
    case 'relative':
      if (targetDate.toDateString() === today.toDateString()) {
        return '今天'
      } else if (targetDate.toDateString() === yesterday.toDateString()) {
        return '昨天'
      } else {
        return `${targetDate.getMonth() + 1}月${targetDate.getDate()}日`
      }
    
    case 'full':
      return `${targetDate.getFullYear()}年${targetDate.getMonth() + 1}月${targetDate.getDate()}日`
    
    case 'short':
      return `${targetDate.getMonth() + 1}/${targetDate.getDate()}`
    
    default:
      return targetDate.toLocaleDateString()
  }
}

/**
 * 获取月份文本
 * @param {Number} year 年份
 * @param {Number} month 月份 (0-11)
 */
export function getMonthText(year, month) {
  return `${year}年${month + 1}月`
}

/**
 * 获取当前月份的第一天和最后一天
 * @param {Number} year 年份
 * @param {Number} month 月份 (0-11)
 */
export function getMonthRange(year, month) {
  const firstDay = new Date(year, month, 1)
  const lastDay = new Date(year, month + 1, 0)
  
  return {
    start: firstDay.toISOString().split('T')[0],
    end: lastDay.toISOString().split('T')[0]
  }
}

/**
 * 检查日期是否在指定月份
 * @param {String|Date} date 要检查的日期
 * @param {Number} year 年份
 * @param {Number} month 月份 (0-11)
 */
export function isInMonth(date, year, month) {
  const targetDate = new Date(date)
  return targetDate.getFullYear() === year && targetDate.getMonth() === month
}

/**
 * 获取今天的日期字符串 (YYYY-MM-DD)
 */
export function getTodayString() {
  return new Date().toISOString().split('T')[0]
}

/**
 * 获取昨天的日期字符串 (YYYY-MM-DD)
 */
export function getYesterdayString() {
  const yesterday = new Date()
  yesterday.setDate(yesterday.getDate() - 1)
  return yesterday.toISOString().split('T')[0]
}

/**
 * 获取本周的开始和结束日期
 */
export function getWeekRange() {
  const today = new Date()
  const dayOfWeek = today.getDay()
  const startOfWeek = new Date(today)
  startOfWeek.setDate(today.getDate() - dayOfWeek)
  
  const endOfWeek = new Date(startOfWeek)
  endOfWeek.setDate(startOfWeek.getDate() + 6)
  
  return {
    start: startOfWeek.toISOString().split('T')[0],
    end: endOfWeek.toISOString().split('T')[0]
  }
}

/**
 * 获取本月的开始和结束日期
 */
export function getCurrentMonthRange() {
  const today = new Date()
  return getMonthRange(today.getFullYear(), today.getMonth())
}

/**
 * 获取本年的开始和结束日期
 */
export function getCurrentYearRange() {
  const today = new Date()
  const year = today.getFullYear()
  
  return {
    start: `${year}-01-01`,
    end: `${year}-12-31`
  }
}

/**
 * 计算两个日期之间的天数差
 * @param {String|Date} startDate 开始日期
 * @param {String|Date} endDate 结束日期
 */
export function getDaysDiff(startDate, endDate) {
  const start = new Date(startDate)
  const end = new Date(endDate)
  const diffTime = Math.abs(end - start)
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
}

/**
 * 检查是否是今天
 * @param {String|Date} date 要检查的日期
 */
export function isToday(date) {
  const targetDate = new Date(date)
  const today = new Date()
  return targetDate.toDateString() === today.toDateString()
}

/**
 * 检查是否是昨天
 * @param {String|Date} date 要检查的日期
 */
export function isYesterday(date) {
  const targetDate = new Date(date)
  const yesterday = new Date()
  yesterday.setDate(yesterday.getDate() - 1)
  return targetDate.toDateString() === yesterday.toDateString()
}

/**
 * 获取友好的时间显示
 * @param {String|Date} date 日期
 */
export function getFriendlyTime(date) {
  const targetDate = new Date(date)
  const now = new Date()
  const diffMs = now - targetDate
  const diffMins = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
  
  if (diffMins < 1) {
    return '刚刚'
  } else if (diffMins < 60) {
    return `${diffMins}分钟前`
  } else if (diffHours < 24) {
    return `${diffHours}小时前`
  } else if (diffDays < 7) {
    return `${diffDays}天前`
  } else {
    return formatDate(date, 'full')
  }
}
