<template>
  <view class="container">


    <!-- 时间范围选择 -->
    <view class="time-selector">
      <view class="selector-tabs">
        <view
          v-for="(tab, index) in timeTabs"
          :key="index"
          :class="['tab-item', currentTab === index ? 'active' : '']"
          @click="switchTab(index)"
        >
          {{ tab.name }}
        </view>
      </view>

      <!-- 自定义时间选择 -->
      <view v-if="currentTab === 4" class="custom-time">
        <picker mode="date" :value="customStartDate" @change="onStartDateChange">
          <view class="date-picker">{{ formatDate(customStartDate) }}</view>
        </picker>
        <text class="date-separator">至</text>
        <picker mode="date" :value="customEndDate" @change="onEndDateChange">
          <view class="date-picker">{{ formatDate(customEndDate) }}</view>
        </picker>
      </view>

      <!-- 年月选择器 -->
      <view v-else-if="currentTab === 0" class="month-selector">
        <button class="nav-btn" @click="changeMonth(-1)">‹</button>
        <text class="current-period">{{ currentPeriodText }}</text>
        <button class="nav-btn" @click="changeMonth(1)">›</button>
      </view>

      <!-- 年份选择器 -->
      <view v-else-if="currentTab === 1" class="year-selector">
        <button class="nav-btn" @click="changeYear(-1)">‹</button>
        <text class="current-period">{{ currentYear }}年</text>
        <button class="nav-btn" @click="changeYear(1)">›</button>
      </view>
    </view>

    <!-- 核心数据卡片 -->
    <view class="stats-overview">
      <view class="stats-card income-card">
        <view class="stats-icon">💰</view>
        <view class="stats-content">
          <text class="stats-label">总收入</text>
          <text class="stats-amount">¥{{ currentStats.income.toFixed(0) }}</text>
          <text class="stats-count">{{ currentStats.incomeCount }}笔</text>
        </view>
      </view>

      <view class="stats-card expense-card">
        <view class="stats-icon">💸</view>
        <view class="stats-content">
          <text class="stats-label">总支出</text>
          <text class="stats-amount">¥{{ currentStats.expense.toFixed(0) }}</text>
          <text class="stats-count">{{ currentStats.expenseCount }}笔</text>
        </view>
      </view>

      <view class="stats-card balance-card">
        <view class="stats-icon">📊</view>
        <view class="stats-content">
          <text class="stats-label">净结余</text>
          <text class="stats-amount" :class="currentBalance >= 0 ? 'positive' : 'negative'">
            {{ currentBalance >= 0 ? '+' : '' }}¥{{ Math.abs(currentBalance).toFixed(0) }}
          </text>
          <text class="stats-trend">{{ balanceTrend }}</text>
        </view>
      </view>
    </view>



    <!-- 分类统计 -->
    <view v-if="hasData" class="category-analysis">
      <view class="analysis-tabs">
        <view
          :class="['analysis-tab', showIncomeAnalysis ? 'active' : '']"
          @click="showIncomeAnalysis = true"
        >
          收入分析
        </view>
        <view
          :class="['analysis-tab', !showIncomeAnalysis ? 'active' : '']"
          @click="showIncomeAnalysis = false"
        >
          支出分析
        </view>
      </view>

      <view v-if="showIncomeAnalysis && incomeCategories.length > 0" class="category-list">
        <view
          v-for="(item, index) in incomeCategories"
          :key="item.category"
          class="category-item"
        >
          <view class="category-rank">{{ index + 1 }}</view>
          <view class="category-info">
            <text class="category-name">{{ item.category }}</text>
            <text class="category-details">{{ item.count }}笔 · 平均¥{{ (item.amount / item.count).toFixed(0) }}</text>
          </view>
          <view class="category-amount income-amount">
            ¥{{ item.amount.toFixed(0) }}
          </view>
          <view class="category-percentage">
            {{ ((item.amount / currentStats.income) * 100).toFixed(1) }}%
          </view>
        </view>
      </view>

      <view v-if="!showIncomeAnalysis && expenseCategories.length > 0" class="category-list">
        <view
          v-for="(item, index) in expenseCategories"
          :key="item.category"
          class="category-item"
        >
          <view class="category-rank">{{ index + 1 }}</view>
          <view class="category-info">
            <text class="category-name">{{ item.category }}</text>
            <text class="category-details">{{ item.count }}笔 · 平均¥{{ (item.amount / item.count).toFixed(0) }}</text>
          </view>
          <view class="category-amount expense-amount">
            ¥{{ item.amount.toFixed(0) }}
          </view>
          <view class="category-percentage">
            {{ ((item.amount / currentStats.expense) * 100).toFixed(1) }}%
          </view>
        </view>
      </view>
    </view>

    <!-- 无数据提示 -->
    <view v-if="!hasData" class="empty-state">
      <view class="empty-icon">📊</view>
      <text class="empty-title">暂无数据</text>
      <text class="empty-subtitle">{{ emptyMessage }}</text>
    </view>

    <!-- 自定义TabBar -->
    <custom-tab-bar></custom-tab-bar>
  </view>
</template>

<script>
import CustomTabBar from '../../custom-tab-bar/index.vue'

export default {
  components: {
    CustomTabBar
  },
  data() {
    return {
      currentYear: new Date().getFullYear(),
      currentMonth: new Date().getMonth(),
      currentTab: 0, // 0:月度 1:年度 2:季度 3:半年 4:自定义
      customStartDate: '',
      customEndDate: '',
      showIncomeAnalysis: true,
      records: [],
      currentStats: {
        income: 0,
        expense: 0,
        incomeCount: 0,
        expenseCount: 0
      },
      incomeCategories: [],
      expenseCategories: [],

      timeTabs: [
        { name: '月度', key: 'month' },
        { name: '年度', key: 'year' },
        { name: '季度', key: 'quarter' },
        { name: '半年', key: 'halfYear' },
        { name: '自定义', key: 'custom' }
      ]
    }
  },

  computed: {
    currentPeriodText() {
      if (this.currentTab === 0) {
        return `${this.currentYear}年${this.currentMonth + 1}月`
      } else if (this.currentTab === 1) {
        return `${this.currentYear}年`
      } else if (this.currentTab === 2) {
        const quarter = Math.floor(this.currentMonth / 3) + 1
        return `${this.currentYear}年第${quarter}季度`
      } else if (this.currentTab === 3) {
        const half = this.currentMonth < 6 ? '上半年' : '下半年'
        return `${this.currentYear}年${half}`
      }
      return ''
    },

    currentBalance() {
      return this.currentStats.income - this.currentStats.expense
    },

    balanceTrend() {
      if (this.currentBalance > 0) return '盈余'
      else if (this.currentBalance < 0) return '亏损'
      else return '平衡'
    },

    hasData() {
      return this.currentStats.income > 0 || this.currentStats.expense > 0
    },

    emptyMessage() {
      const tabNames = ['本月', '本年', '本季度', '本半年', '所选时间段']
      return `${tabNames[this.currentTab]}还没有记录`
    },


  },
  
  onShow() {
    this.loadData()
    this.initCustomDates()
    // 通知TabBar更新选中状态
    this.$nextTick(() => {
      uni.$emit('tabBarUpdate')
    })
  },

  methods: {
    loadData() {
      this.records = uni.getStorageSync('accountRecords') || []
      this.calculateStats()
    },

    initCustomDates() {
      const today = new Date()
      const firstDay = new Date(today.getFullYear(), today.getMonth(), 1)
      this.customStartDate = firstDay.toISOString().split('T')[0]
      this.customEndDate = today.toISOString().split('T')[0]
    },

    switchTab(index) {
      this.currentTab = index
      this.calculateStats()
    },

    changeMonth(direction) {
      this.currentMonth += direction
      if (this.currentMonth < 0) {
        this.currentMonth = 11
        this.currentYear--
      } else if (this.currentMonth > 11) {
        this.currentMonth = 0
        this.currentYear++
      }
      this.calculateStats()
    },

    changeYear(direction) {
      this.currentYear += direction
      this.calculateStats()
    },

    onStartDateChange(e) {
      this.customStartDate = e.detail.value
      if (this.currentTab === 4) {
        this.calculateStats()
      }
    },

    onEndDateChange(e) {
      this.customEndDate = e.detail.value
      if (this.currentTab === 4) {
        this.calculateStats()
      }
    },

    formatDate(dateStr) {
      const date = new Date(dateStr)
      return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`
    },
    
    calculateStats() {
      // 根据当前选择的时间范围筛选记录
      const filteredRecords = this.getFilteredRecords()

      // 重置统计数据
      this.currentStats = { income: 0, expense: 0, incomeCount: 0, expenseCount: 0 }
      const incomeMap = new Map()
      const expenseMap = new Map()

      // 计算统计数据
      filteredRecords.forEach(record => {
        const amount = parseFloat(record.amount)

        if (record.type === 'income') {
          this.currentStats.income += amount
          this.currentStats.incomeCount++

          if (incomeMap.has(record.category)) {
            const existing = incomeMap.get(record.category)
            existing.amount += amount
            existing.count++
          } else {
            incomeMap.set(record.category, {
              category: record.category,
              amount: amount,
              count: 1
            })
          }
        } else {
          this.currentStats.expense += amount
          this.currentStats.expenseCount++

          if (expenseMap.has(record.category)) {
            const existing = expenseMap.get(record.category)
            existing.amount += amount
            existing.count++
          } else {
            expenseMap.set(record.category, {
              category: record.category,
              amount: amount,
              count: 1
            })
          }
        }
      })

      // 转换为数组并排序
      this.incomeCategories = Array.from(incomeMap.values())
        .sort((a, b) => b.amount - a.amount)

      this.expenseCategories = Array.from(expenseMap.values())
        .sort((a, b) => b.amount - a.amount)
    },

    getFilteredRecords() {
      return this.records.filter(record => {
        const recordDate = new Date(record.date)

        if (this.currentTab === 0) { // 月度
          return recordDate.getFullYear() === this.currentYear &&
                 recordDate.getMonth() === this.currentMonth
        } else if (this.currentTab === 1) { // 年度
          return recordDate.getFullYear() === this.currentYear
        } else if (this.currentTab === 2) { // 季度
          const quarter = Math.floor(this.currentMonth / 3)
          const recordQuarter = Math.floor(recordDate.getMonth() / 3)
          return recordDate.getFullYear() === this.currentYear &&
                 recordQuarter === quarter
        } else if (this.currentTab === 3) { // 半年
          const isFirstHalf = this.currentMonth < 6
          const recordIsFirstHalf = recordDate.getMonth() < 6
          return recordDate.getFullYear() === this.currentYear &&
                 recordIsFirstHalf === isFirstHalf
        } else if (this.currentTab === 4) { // 自定义
          const startDate = new Date(this.customStartDate)
          const endDate = new Date(this.customEndDate)
          endDate.setHours(23, 59, 59, 999) // 包含结束日期的全天
          return recordDate >= startDate && recordDate <= endDate
        }
        return false
      })
    },


  }
}
</script>

<style scoped>
.container {
  background: linear-gradient(135deg, #fefefe 0%, #f8f8f8 25%, #f5f5f5 50%, #f2f2f2 75%, #efefef 100%);
  background-size: 400% 400%;
  animation: gradientShift 20s ease-in-out infinite;
  min-height: 100vh;
  padding-bottom: 180rpx; /* 为TabBar留出空间 */
  position: relative;
  overflow: hidden;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  25% { background-position: 100% 25%; }
  50% { background-position: 50% 100%; }
  75% { background-position: 25% 0%; }
  100% { background-position: 0% 50%; }
}

.container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.4), transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(255, 255, 255, 0.3), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(255, 255, 255, 0.5), transparent);
  background-repeat: repeat;
  background-size: 200px 100px;
  animation: snowfall 15s linear infinite;
  pointer-events: none;
}

@keyframes backgroundFloat {
  0%, 100% {
    transform: translateX(0) translateY(0) scale(1);
    opacity: 0.8;
  }
  33% {
    transform: translateX(10px) translateY(-5px) scale(1.02);
    opacity: 0.9;
  }
  66% {
    transform: translateX(-5px) translateY(8px) scale(0.98);
    opacity: 0.85;
  }
}

@keyframes snowfall {
  0% {
    transform: translateY(-100vh) translateX(0px);
  }
  100% {
    transform: translateY(100vh) translateX(100px);
  }
}

/* 顶部标题 */
.header {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(40rpx);
  -webkit-backdrop-filter: blur(40rpx);
  color: #2d3748;
  text-align: center;
  padding: 60rpx 40rpx 40rpx;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.3);
  position: relative;
  z-index: 2;
  box-shadow:
    0 8rpx 32rpx rgba(0, 0, 0, 0.04),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.6);
}

.title {
  font-size: 52rpx;
  font-weight: 200;
  margin-bottom: 20rpx;
  color: #1a202c;
  letter-spacing: 4rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.subtitle {
  font-size: 30rpx;
  color: #4a5568;
  font-weight: 300;
  letter-spacing: 1rpx;
  opacity: 0.9;
}

/* 时间选择器 */
.time-selector {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(40rpx);
  -webkit-backdrop-filter: blur(40rpx);
  border-radius: 32rpx;
  margin: 32rpx;
  padding: 40rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 20rpx 60rpx rgba(0, 0, 0, 0.1),
    0 8rpx 24rpx rgba(0, 0, 0, 0.08),
    inset 0 2rpx 0 rgba(255, 255, 255, 0.3);
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

.selector-tabs {
  display: flex;
  background: rgba(241, 245, 249, 0.8);
  border-radius: 20rpx;
  padding: 6rpx;
  margin-bottom: 24rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 24rpx 16rpx;
  border-radius: 20rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #2D3748;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 3rpx solid transparent;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.tab-item.active {
  background: rgba(255, 255, 255, 0.95);
  color: #000000;
  font-weight: 800;
  border: 3rpx solid #22C55E;
  box-shadow:
    0 6rpx 20rpx rgba(0, 0, 0, 0.12),
    0 0 0 2rpx rgba(34, 197, 94, 0.2);
  transform: scale(1.05);
}

.custom-time {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.date-picker {
  flex: 1;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 16rpx;
  text-align: center;
  font-size: 28rpx;
  color: #2d3748;
}

.date-separator {
  font-size: 28rpx;
  color: #4a5568;
  font-weight: 300;
}

.month-selector, .year-selector {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 32rpx;
}

.nav-btn {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.4);
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10rpx);
  color: #1A202C;
  font-size: 40rpx;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow:
    0 6rpx 20rpx rgba(0, 0, 0, 0.12),
    0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.nav-btn:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.3);
}

.current-period {
  font-size: 42rpx;
  font-weight: 700;
  color: #000000;
  letter-spacing: 3rpx;
  min-width: 240rpx;
  text-align: center;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 统计概览卡片 */
.stats-overview {
  display: flex;
  gap: 16rpx;
  margin: 32rpx;
  position: relative;
  z-index: 2;
}

.stats-card {
  flex: 1;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(30rpx);
  -webkit-backdrop-filter: blur(30rpx);
  border-radius: 28rpx;
  padding: 32rpx 24rpx;
  text-align: center;
  position: relative;
  box-shadow:
    0 16rpx 48rpx rgba(0, 0, 0, 0.06),
    0 4rpx 16rpx rgba(0, 0, 0, 0.04),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.7);
  border: 1rpx solid rgba(255, 255, 255, 0.4);
  transition: all 0.4s ease;
}

.stats-card:hover {
  transform: translateY(-4rpx);
  box-shadow:
    0 24rpx 64rpx rgba(0, 0, 0, 0.08),
    0 8rpx 24rpx rgba(0, 0, 0, 0.06),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
}

.stats-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
  opacity: 0.9;
}

.stats-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.stats-label {
  font-size: 30rpx;
  color: #2D3748;
  font-weight: 600;
  letter-spacing: 1rpx;
}

.stats-amount {
  font-size: 44rpx;
  font-weight: 800;
  color: #000000;
  letter-spacing: 2rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.stats-amount.positive {
  color: #38a169;
}

.stats-amount.negative {
  color: #e53e3e;
}

.stats-count {
  font-size: 22rpx;
  color: #718096;
  font-weight: 300;
}

.stats-trend {
  font-size: 22rpx;
  color: #4a5568;
  font-weight: 300;
  opacity: 0.8;
}



/* 分类分析 */
.category-analysis {
  margin: 32rpx;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(30rpx);
  -webkit-backdrop-filter: blur(30rpx);
  border-radius: 32rpx;
  padding: 40rpx 32rpx;
  position: relative;
  z-index: 2;
  box-shadow:
    0 16rpx 48rpx rgba(0, 0, 0, 0.06),
    0 4rpx 16rpx rgba(0, 0, 0, 0.04),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.7);
  border: 1rpx solid rgba(255, 255, 255, 0.4);
}

.analysis-tabs {
  display: flex;
  background: rgba(241, 245, 249, 0.8);
  border-radius: 20rpx;
  padding: 6rpx;
  margin-bottom: 32rpx;
}

.analysis-tab {
  flex: 1;
  text-align: center;
  padding: 24rpx 16rpx;
  border-radius: 20rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #2D3748;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 3rpx solid transparent;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.analysis-tab.active {
  background: rgba(255, 255, 255, 0.95);
  color: #000000;
  font-weight: 800;
  border: 3rpx solid #3B82F6;
  box-shadow:
    0 6rpx 20rpx rgba(0, 0, 0, 0.12),
    0 0 0 2rpx rgba(59, 130, 246, 0.2);
  transform: scale(1.05);
}

/* 分类列表 */
.category-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.category-item {
  display: flex;
  align-items: center;
  padding: 24rpx 20rpx;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.category-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateX(4rpx);
}

.category-rank {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 400;
  color: #4a5568;
  margin-right: 20rpx;
}

.category-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.category-name {
  font-size: 36rpx;
  font-weight: 700;
  color: #000000;
  letter-spacing: 1rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.category-details {
  font-size: 28rpx;
  color: #2D3748;
  font-weight: 500;
  opacity: 0.9;
}

.category-amount {
  font-size: 38rpx;
  font-weight: 800;
  letter-spacing: 2rpx;
  margin-right: 20rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.category-amount.income-amount {
  color: #38a169;
}

.category-amount.expense-amount {
  color: #e53e3e;
}

.category-percentage {
  font-size: 26rpx;
  color: #4a5568;
  font-weight: 300;
  opacity: 0.8;
  min-width: 60rpx;
  text-align: right;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 48rpx;
  margin: 32rpx;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(30rpx);
  -webkit-backdrop-filter: blur(30rpx);
  border-radius: 32rpx;
  position: relative;
  z-index: 2;
  box-shadow:
    0 16rpx 48rpx rgba(0, 0, 0, 0.06),
    0 4rpx 16rpx rgba(0, 0, 0, 0.04),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.7);
  border: 1rpx solid rgba(255, 255, 255, 0.4);
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 36rpx;
  font-weight: 300;
  color: #2d3748;
  margin-bottom: 16rpx;
  letter-spacing: 1rpx;
}

.empty-subtitle {
  font-size: 28rpx;
  color: #4a5568;
  font-weight: 300;
  opacity: 0.8;
}
</style>
