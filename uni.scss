/**
 * 这里是uni-app内置的常用样式变量
 * 为了方便开发者使用，将一些常用的颜色值定义为scss变量
 * 注意：这些变量在vue文件中无法使用，如果需要使用，请在vue文件中重新定义
 */

/* 颜色变量 */

/* 行为相关颜色 */
$uni-color-primary: #4CAF50;
$uni-color-success: #4CAF50;
$uni-color-warning: #ff9500;
$uni-color-error: #f44336;

/* 文字基本颜色 */
$uni-text-color: #333; // 主要文字
$uni-text-color-inverse: #fff; // 反色文字
$uni-text-color-grey: #999; // 辅助灰色，如加载更多的提示信息
$uni-text-color-placeholder: #808080;
$uni-text-color-disable: #c0c0c0;

/* 背景颜色 */
$uni-bg-color: #ffffff;
$uni-bg-color-grey: #f8f8f8;
$uni-bg-color-hover: #f1f1f1; // 点击状态颜色
$uni-bg-color-mask: rgba(0, 0, 0, 0.4); // 遮罩颜色

/* 边框颜色 */
$uni-border-color: #e5e5e5;

/* 尺寸变量 */

/* 文字尺寸 */
$uni-font-size-sm: 24rpx;
$uni-font-size-base: 28rpx;
$uni-font-size-lg: 32rpx;

/* 图片尺寸 */
$uni-img-size-sm: 40rpx;
$uni-img-size-base: 52rpx;
$uni-img-size-lg: 80rpx;

/* Border Radius */
$uni-border-radius-sm: 4rpx;
$uni-border-radius-base: 6rpx;
$uni-border-radius-lg: 12rpx;
$uni-border-radius-circle: 50%;

/* 水平间距 */
$uni-spacing-row-sm: 10rpx;
$uni-spacing-row-base: 20rpx;
$uni-spacing-row-lg: 30rpx;

/* 垂直间距 */
$uni-spacing-col-sm: 8rpx;
$uni-spacing-col-base: 16rpx;
$uni-spacing-col-lg: 24rpx;

/* 透明度 */
$uni-opacity-disabled: 0.3; // 组件禁用态的透明度

/* 文章场景相关 */
$uni-color-title: #2c405a; // 文章标题颜色
$uni-color-subtitle: #555555; // 二级标题颜色
$uni-color-paragraph: #3f536e; // 文章段落颜色
