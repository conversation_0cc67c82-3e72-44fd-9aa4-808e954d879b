/**
 * 本地存储工具类
 * 专门用于处理记账数据的存储和读取
 */

// 存储键名常量
const STORAGE_KEYS = {
  RECORDS: 'accountRecords',
  CATEGORIES: 'categories',
  SETTINGS: 'appSettings'
}

/**
 * 获取所有记账记录
 */
export function getRecords() {
  try {
    return uni.getStorageSync(STORAGE_KEYS.RECORDS) || []
  } catch (error) {
    console.error('获取记录失败:', error)
    return []
  }
}

/**
 * 保存记账记录
 * @param {Array} records 记录数组
 */
export function saveRecords(records) {
  try {
    uni.setStorageSync(STORAGE_KEYS.RECORDS, records)
    return true
  } catch (error) {
    console.error('保存记录失败:', error)
    return false
  }
}

/**
 * 添加单条记录
 * @param {Object} record 记录对象
 */
export function addRecord(record) {
  try {
    const records = getRecords()
    record.id = Date.now() // 生成简单ID
    record.createTime = new Date().toISOString()
    records.push(record)
    return saveRecords(records)
  } catch (error) {
    console.error('添加记录失败:', error)
    return false
  }
}

/**
 * 删除记录
 * @param {Number} recordId 记录ID
 */
export function deleteRecord(recordId) {
  try {
    const records = getRecords()
    const index = records.findIndex(r => r.id === recordId)
    if (index > -1) {
      records.splice(index, 1)
      return saveRecords(records)
    }
    return false
  } catch (error) {
    console.error('删除记录失败:', error)
    return false
  }
}

/**
 * 获取分类数据
 */
export function getCategories() {
  try {
    const categories = uni.getStorageSync(STORAGE_KEYS.CATEGORIES)
    if (!categories) {
      // 返回默认分类
      const defaultCategories = {
        income: ['工资', '奖金', '理财', '其他收入'],
        expense: ['餐饮', '交通', '购物', '医疗', '娱乐', '生活用品', '其他支出']
      }
      saveCategories(defaultCategories)
      return defaultCategories
    }
    return categories
  } catch (error) {
    console.error('获取分类失败:', error)
    return {
      income: ['工资', '奖金', '理财', '其他收入'],
      expense: ['餐饮', '交通', '购物', '医疗', '娱乐', '生活用品', '其他支出']
    }
  }
}

/**
 * 保存分类数据
 * @param {Object} categories 分类对象
 */
export function saveCategories(categories) {
  try {
    uni.setStorageSync(STORAGE_KEYS.CATEGORIES, categories)
    return true
  } catch (error) {
    console.error('保存分类失败:', error)
    return false
  }
}

/**
 * 获取应用设置
 */
export function getSettings() {
  try {
    return uni.getStorageSync(STORAGE_KEYS.SETTINGS) || {
      theme: 'light',
      fontSize: 'normal',
      currency: 'CNY'
    }
  } catch (error) {
    console.error('获取设置失败:', error)
    return {
      theme: 'light',
      fontSize: 'normal', 
      currency: 'CNY'
    }
  }
}

/**
 * 保存应用设置
 * @param {Object} settings 设置对象
 */
export function saveSettings(settings) {
  try {
    uni.setStorageSync(STORAGE_KEYS.SETTINGS, settings)
    return true
  } catch (error) {
    console.error('保存设置失败:', error)
    return false
  }
}

/**
 * 清空所有数据（慎用）
 */
export function clearAllData() {
  try {
    uni.removeStorageSync(STORAGE_KEYS.RECORDS)
    uni.removeStorageSync(STORAGE_KEYS.CATEGORIES)
    uni.removeStorageSync(STORAGE_KEYS.SETTINGS)
    return true
  } catch (error) {
    console.error('清空数据失败:', error)
    return false
  }
}

/**
 * 导出数据（用于备份）
 */
export function exportData() {
  try {
    return {
      records: getRecords(),
      categories: getCategories(),
      settings: getSettings(),
      exportTime: new Date().toISOString()
    }
  } catch (error) {
    console.error('导出数据失败:', error)
    return null
  }
}

/**
 * 导入数据（用于恢复）
 * @param {Object} data 要导入的数据
 */
export function importData(data) {
  try {
    if (data.records) {
      saveRecords(data.records)
    }
    if (data.categories) {
      saveCategories(data.categories)
    }
    if (data.settings) {
      saveSettings(data.settings)
    }
    return true
  } catch (error) {
    console.error('导入数据失败:', error)
    return false
  }
}
